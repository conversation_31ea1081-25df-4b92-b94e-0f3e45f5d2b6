#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断绩效计算问题
"""

import os
import pandas as pd
from simple_calculator import SimplePerformanceCalculator

def diagnose_issue():
    print("🔍 诊断绩效计算问题")
    print("=" * 50)
    
    # 1. 检查测试数据文件
    print("1. 📁 检查测试数据文件...")
    test_files = {
        'patients.xlsx': 'test_data/patients.xlsx',
        '1.csv': 'test_data/1.csv',
        '2.csv': 'test_data/2.csv', 
        '3.csv': 'test_data/3.csv',
        'icu.csv': 'test_data/icu.csv'
    }
    
    files_ok = True
    for filename, filepath in test_files.items():
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"   ✅ {filename}: {size} 字节")
            
            # 详细检查CSV文件内容
            if filename.endswith('.csv'):
                try:
                    df = pd.read_csv(filepath)
                    print(f"      行数: {len(df)}, 列: {list(df.columns)}")
                    if len(df) > 0:
                        print(f"      示例数据: {df.iloc[0].to_dict()}")
                except Exception as e:
                    print(f"      ❌ 读取失败: {e}")
                    files_ok = False
        else:
            print(f"   ❌ {filename}: 文件不存在")
            files_ok = False
    
    if not files_ok:
        print("❌ 测试文件检查失败")
        return
    
    # 2. 测试简化计算器
    print("\n2. 🧮 测试简化计算器...")
    try:
        calculator = SimplePerformanceCalculator()
        print("   ✅ 计算器初始化成功")
        
        # 测试患者数据加载
        if calculator.load_patient_data('test_data/patients.xlsx'):
            print(f"   ✅ 患者数据加载成功: {len(calculator.patient_data)} 条")
        else:
            print("   ❌ 患者数据加载失败")
            return
        
        # 测试绩效数据加载
        csv_files = {k: v for k, v in test_files.items() if k.endswith('.csv')}
        if calculator.load_performance_data(csv_files):
            print("   ✅ 绩效数据加载成功")
        else:
            print("   ❌ 绩效数据加载失败")
            return
        
        # 测试计算
        results = calculator.calculate_performance()
        if results:
            print("   ✅ 绩效计算成功")
            print(f"      医生数: {results['summary']['total_doctors']}")
            print(f"      分组数: {results['summary']['total_groups']}")
        else:
            print("   ❌ 绩效计算失败")
            return
            
    except Exception as e:
        print(f"   ❌ 计算器测试异常: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 3. 检查Web应用文件上传目录
    print("\n3. 📂 检查Web应用目录...")
    upload_dir = "uploads"
    if not os.path.exists(upload_dir):
        print(f"   ❌ 上传目录不存在: {upload_dir}")
        os.makedirs(upload_dir, exist_ok=True)
        print(f"   ✅ 已创建上传目录: {upload_dir}")
    else:
        print(f"   ✅ 上传目录存在: {upload_dir}")
    
    output_dir = "output"
    if not os.path.exists(output_dir):
        print(f"   ❌ 输出目录不存在: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)
        print(f"   ✅ 已创建输出目录: {output_dir}")
    else:
        print(f"   ✅ 输出目录存在: {output_dir}")
    
    # 4. 测试模拟Web上传场景
    print("\n4. 🌐 模拟Web上传场景...")
    test_upload_dir = "uploads/test_diagnosis"
    os.makedirs(test_upload_dir, exist_ok=True)
    
    try:
        # 复制文件到上传目录
        import shutil
        for filename, source_path in test_files.items():
            dest_path = os.path.join(test_upload_dir, filename)
            shutil.copy2(source_path, dest_path)
            print(f"   📋 复制 {filename} -> {dest_path}")
        
        # 测试Web计算函数
        from app import perform_calculation
        result = perform_calculation(
            task_id="diagnosis_test",
            data_folder=test_upload_dir,
            duplicate_strategy="keep_first"
        )
        
        if result['success']:
            print("   ✅ Web计算函数测试成功")
            print(f"      输出文件: {result['output_file']}")
            if 'stats' in result:
                print(f"      统计: {result['stats']}")
        else:
            print(f"   ❌ Web计算函数测试失败: {result['error']}")
            
            # 尝试手动调试
            print("\n   🔍 手动调试Web计算过程...")
            try:
                calc = SimplePerformanceCalculator()
                
                # 检查上传文件
                print("      检查上传文件:")
                for filename in ['patients.xlsx', '1.csv', '2.csv', '3.csv', 'icu.csv']:
                    filepath = os.path.join(test_upload_dir, filename)
                    if os.path.exists(filepath):
                        size = os.path.getsize(filepath)
                        print(f"        ✅ {filename}: {size} 字节")
                    else:
                        print(f"        ❌ {filename}: 文件不存在")
                
                # 测试每个步骤
                patients_file = os.path.join(test_upload_dir, 'patients.xlsx')
                if calc.load_patient_data(patients_file):
                    print("      ✅ 患者数据加载成功")
                else:
                    print("      ❌ 患者数据加载失败")
                    return
                
                csv_files_web = {
                    '1.csv': os.path.join(test_upload_dir, '1.csv'),
                    '2.csv': os.path.join(test_upload_dir, '2.csv'),
                    '3.csv': os.path.join(test_upload_dir, '3.csv'),
                    'icu.csv': os.path.join(test_upload_dir, 'icu.csv')
                }
                
                if calc.load_performance_data(csv_files_web):
                    print("      ✅ 绩效数据加载成功")
                else:
                    print("      ❌ 绩效数据加载失败")
                    print("      📊 详细信息:")
                    print(f"         performance_data keys: {list(calc.performance_data.keys())}")
                    print(f"         performance_data 长度: {len(calc.performance_data)}")
                    
            except Exception as e:
                print(f"      ❌ 手动调试异常: {e}")
                import traceback
                traceback.print_exc()
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_upload_dir)
            print(f"   🧹 清理测试目录: {test_upload_dir}")
        except:
            pass
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")
    print("\n💡 如果Web应用中仍然出现'绩效数据加载失败'：")
    print("1. 确保所有5个文件都正确上传")
    print("2. 检查文件名是否完全匹配 (patients.xlsx, 1.csv, 2.csv, 3.csv, icu.csv)")
    print("3. 查看Flask控制台的详细错误信息")
    print("4. 确认文件内容格式正确")

if __name__ == "__main__":
    diagnose_issue()