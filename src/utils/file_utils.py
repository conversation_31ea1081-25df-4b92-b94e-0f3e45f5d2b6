#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作工具模块
提供文件和文件夹操作的通用功能
"""

import os
import pandas as pd
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import logging

from .logger import get_logger

logger = get_logger()

def validate_folder_structure(folder_path: str) -> Tuple[bool, List[str], List[str]]:
    """
    验证文件夹结构是否符合要求
    
    Args:
        folder_path: 文件夹路径
    
    Returns:
        (是否有效, 找到的文件列表, 缺失的文件列表)
    """
    required_files = [
        "patients.xlsx",
        "1.csv",
        "2.csv", 
        "3.csv",
        "icu.csv"
    ]
    
    found_files = []
    missing_files = []
    
    if not os.path.exists(folder_path):
        logger.error(f"文件夹不存在: {folder_path}")
        return False, [], required_files
    
    for file_name in required_files:
        file_path = os.path.join(folder_path, file_name)
        if os.path.exists(file_path):
            found_files.append(file_name)
            logger.info(f"找到文件: {file_name}")
        else:
            missing_files.append(file_name)
            logger.warning(f"缺失文件: {file_name}")
    
    is_valid = len(missing_files) == 0
    return is_valid, found_files, missing_files

def get_file_info(file_path: str) -> Dict[str, any]:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
    
    Returns:
        文件信息字典
    """
    if not os.path.exists(file_path):
        return {"exists": False}
    
    stat = os.stat(file_path)
    return {
        "exists": True,
        "size": stat.st_size,
        "modified": stat.st_mtime,
        "name": os.path.basename(file_path),
        "extension": os.path.splitext(file_path)[1]
    }

def safe_read_excel(file_path: str, sheet_name: Optional[str] = None) -> Optional[pd.DataFrame]:
    """
    安全读取Excel文件
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称，None表示读取第一个工作表
    
    Returns:
        DataFrame或None（如果读取失败）
    """
    try:
        logger.info(f"正在读取Excel文件: {file_path}")
        
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        
        logger.info(f"成功读取Excel文件，数据形状: {df.shape}")
        return df
        
    except Exception as e:
        logger.error(f"读取Excel文件失败 {file_path}: {str(e)}")
        return None

def safe_read_csv(file_path: str, encoding: str = 'utf-8') -> Optional[pd.DataFrame]:
    """
    安全读取CSV文件
    
    Args:
        file_path: CSV文件路径
        encoding: 文件编码
    
    Returns:
        DataFrame或None（如果读取失败）
    """
    try:
        logger.info(f"正在读取CSV文件: {file_path}")
        
        # 尝试不同的编码
        encodings = [encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        
        for enc in encodings:
            try:
                df = pd.read_csv(file_path, encoding=enc)
                logger.info(f"成功读取CSV文件（编码: {enc}），数据形状: {df.shape}")
                return df
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，抛出异常
        raise Exception("无法使用任何编码读取文件")
        
    except Exception as e:
        logger.error(f"读取CSV文件失败 {file_path}: {str(e)}")
        return None

def safe_write_excel(df: pd.DataFrame, file_path: str, sheet_name: str = 'Sheet1') -> bool:
    """
    安全写入Excel文件
    
    Args:
        df: 要写入的DataFrame
        file_path: 输出文件路径
        sheet_name: 工作表名称
    
    Returns:
        是否写入成功
    """
    try:
        logger.info(f"正在写入Excel文件: {file_path}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        df.to_excel(file_path, sheet_name=sheet_name, index=False)
        logger.info(f"成功写入Excel文件: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"写入Excel文件失败 {file_path}: {str(e)}")
        return False

def create_output_filename(base_name: str = "绩效统计报告", extension: str = ".xlsx") -> str:
    """
    创建带时间戳的输出文件名
    
    Args:
        base_name: 基础文件名
        extension: 文件扩展名
    
    Returns:
        带时间戳的文件名
    """
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base_name}_{timestamp}{extension}"
