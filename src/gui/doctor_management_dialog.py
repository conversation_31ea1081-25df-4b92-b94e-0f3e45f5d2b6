#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医生管理对话框
提供医生分组的可视化管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional, Callable
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.doctor_manager import DoctorManager
from src.gui.components import ConfirmDialog, InputDialog
from src.utils.logger import get_logger

logger = get_logger()

class DoctorManagementDialog:
    """医生管理对话框"""

    def __init__(self, parent: tk.Tk, doctor_manager: Doctor<PERSON><PERSON><PERSON>, on_update_callback: Optional[Callable] = None):
        """
        初始化医生管理对话框

        Args:
            parent: 父窗口
            doctor_manager: 医生管理器实例
            on_update_callback: 更新回调函数
        """
        self.parent = parent
        self.doctor_manager = doctor_manager
        self.on_update_callback = on_update_callback
        self.dialog = None

        # 界面组件
        self.groups_tree = None
        self.doctors_tree = None
        self.selected_group = None
        self.selected_doctor = None

    def show(self) -> None:
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("医生分组管理")
        self.dialog.geometry("900x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 创建界面
        self._create_widgets(main_frame)
        self._setup_layout(main_frame)
        self._load_data()

        # 居中显示
        self.center_dialog()

    def _create_widgets(self, parent: ttk.Frame) -> None:
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(parent, text="医生分组管理", font=("Arial", 14, "bold"))
        self.title_label = title_label

        # 左侧分组面板
        groups_frame = ttk.LabelFrame(parent, text="医疗分组", padding="5")
        self.groups_frame = groups_frame

        # 分组树形控件
        groups_tree_frame = ttk.Frame(groups_frame)
        self.groups_tree = ttk.Treeview(
            groups_tree_frame,
            columns=("count",),
            show="tree headings",
            height=15
        )
        self.groups_tree.heading("#0", text="分组名称")
        self.groups_tree.heading("count", text="人数")
        self.groups_tree.column("#0", width=150)
        self.groups_tree.column("count", width=60)

        # 分组滚动条
        groups_scrollbar = ttk.Scrollbar(groups_tree_frame, orient="vertical", command=self.groups_tree.yview)
        self.groups_tree.configure(yscrollcommand=groups_scrollbar.set)

        # 分组按钮
        groups_button_frame = ttk.Frame(groups_frame)
        self.add_group_button = ttk.Button(groups_button_frame, text="新建分组", command=self._add_group)
        self.rename_group_button = ttk.Button(groups_button_frame, text="重命名", command=self._rename_group)
        self.delete_group_button = ttk.Button(groups_button_frame, text="删除分组", command=self._delete_group)

        # 右侧医生面板
        doctors_frame = ttk.LabelFrame(parent, text="医生列表 (点击左侧分组查看)", padding="5")
        self.doctors_frame = doctors_frame

        # 当前分组信息标签
        self.current_group_label = ttk.Label(doctors_frame, text="", font=("Arial", 10, "bold"), foreground="blue")
        self.current_group_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

        # 医生树形控件
        doctors_tree_frame = ttk.Frame(doctors_frame)
        self.doctors_tree = ttk.Treeview(
            doctors_tree_frame,
            columns=("group",),
            show="tree headings",
            height=15
        )
        self.doctors_tree.heading("#0", text="医生姓名")
        self.doctors_tree.heading("group", text="所属分组")
        self.doctors_tree.column("#0", width=120)
        self.doctors_tree.column("group", width=120)

        # 医生滚动条
        doctors_scrollbar = ttk.Scrollbar(doctors_tree_frame, orient="vertical", command=self.doctors_tree.yview)
        self.doctors_tree.configure(yscrollcommand=doctors_scrollbar.set)

        # 医生按钮
        self.doctors_button_frame = ttk.Frame(doctors_frame)
        self.add_doctor_to_group_button = ttk.Button(self.doctors_button_frame, text="添加医生到此组", command=self._add_doctor_to_current_group, state="disabled")
        self.remove_from_group_button = ttk.Button(self.doctors_button_frame, text="移出此组", command=self._remove_from_current_group, state="disabled")
        self.move_doctor_button = ttk.Button(self.doctors_button_frame, text="移动到其他组", command=self._move_doctor, state="disabled")
        self.delete_doctor_button = ttk.Button(self.doctors_button_frame, text="删除医生", command=self._delete_doctor, state="disabled")

        # 通用医生管理按钮
        self.general_button_frame = ttk.Frame(doctors_frame)
        self.add_doctor_button = ttk.Button(self.general_button_frame, text="添加新医生", command=self._add_doctor)
        self.view_all_doctors_button = ttk.Button(self.general_button_frame, text="查看所有医生", command=self._view_all_doctors)

        # 底部按钮
        bottom_frame = ttk.Frame(parent)
        self.save_button = ttk.Button(bottom_frame, text="保存配置", command=self._save_configuration)
        self.load_button = ttk.Button(bottom_frame, text="加载配置", command=self._load_configuration)
        self.close_button = ttk.Button(bottom_frame, text="关闭", command=self._close_dialog)

        # 绑定事件
        self.groups_tree.bind("<<TreeviewSelect>>", self._on_group_select)
        self.doctors_tree.bind("<<TreeviewSelect>>", self._on_doctor_select)
        self.doctors_tree.bind("<Double-1>", self._on_doctor_double_click)

    def _setup_layout(self, parent: ttk.Frame) -> None:
        """设置布局"""
        # 标题
        self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 左侧分组面板
        self.groups_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 5))

        # 分组树形控件布局
        groups_tree_frame = self.groups_tree.master
        groups_tree_frame.grid(row=0, column=0, sticky="nsew")
        self.groups_tree.grid(row=0, column=0, sticky="nsew")
        groups_scrollbar = groups_tree_frame.winfo_children()[1]
        groups_scrollbar.grid(row=0, column=1, sticky="ns")
        groups_tree_frame.grid_rowconfigure(0, weight=1)
        groups_tree_frame.grid_columnconfigure(0, weight=1)

        # 分组按钮布局
        groups_button_frame = self.add_group_button.master
        groups_button_frame.grid(row=1, column=0, sticky="ew", pady=(5, 0))
        self.add_group_button.grid(row=0, column=0, padx=(0, 5))
        self.rename_group_button.grid(row=0, column=1, padx=(0, 5))
        self.delete_group_button.grid(row=0, column=2)

        self.groups_frame.grid_rowconfigure(0, weight=1)
        self.groups_frame.grid_columnconfigure(0, weight=1)

        # 右侧医生面板
        self.doctors_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 0))

        # 医生树形控件布局
        doctors_tree_frame = self.doctors_tree.master
        doctors_tree_frame.grid(row=0, column=0, sticky="nsew")
        self.doctors_tree.grid(row=0, column=0, sticky="nsew")
        doctors_scrollbar = doctors_tree_frame.winfo_children()[1]
        doctors_scrollbar.grid(row=0, column=1, sticky="ns")
        doctors_tree_frame.grid_rowconfigure(0, weight=1)
        doctors_tree_frame.grid_columnconfigure(0, weight=1)

        # 医生按钮布局
        self.doctors_button_frame.grid(row=2, column=0, sticky="ew", pady=(5, 0))
        self.add_doctor_to_group_button.grid(row=0, column=0, padx=(0, 5))
        self.remove_from_group_button.grid(row=0, column=1, padx=(0, 5))
        self.move_doctor_button.grid(row=0, column=2, padx=(0, 5))
        self.delete_doctor_button.grid(row=0, column=3)

        # 通用按钮布局
        self.general_button_frame.grid(row=3, column=0, sticky="ew", pady=(5, 0))
        self.add_doctor_button.grid(row=0, column=0, padx=(0, 5))
        self.view_all_doctors_button.grid(row=0, column=1)

        self.doctors_frame.grid_rowconfigure(1, weight=1)
        self.doctors_frame.grid_columnconfigure(0, weight=1)

        # 底部按钮
        bottom_frame = self.save_button.master
        bottom_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.save_button.grid(row=0, column=0, padx=(0, 5))
        self.load_button.grid(row=0, column=1, padx=(0, 5))
        self.close_button.grid(row=0, column=2)

        # 设置权重
        parent.grid_rowconfigure(1, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)

    def _load_data(self) -> None:
        """加载数据到界面"""
        self._refresh_groups()
        self._refresh_doctors()
        self._update_button_states()  # 初始化按钮状态

    def _refresh_groups(self) -> None:
        """刷新分组列表"""
        # 清空现有数据
        for item in self.groups_tree.get_children():
            self.groups_tree.delete(item)

        # 加载分组数据
        group_stats = self.doctor_manager.get_group_statistics()
        for group_name, count in group_stats.items():
            self.groups_tree.insert("", "end", text=group_name, values=(count,))

    def _refresh_doctors(self) -> None:
        """刷新医生列表"""
        # 清空现有数据
        for item in self.doctors_tree.get_children():
            self.doctors_tree.delete(item)

        # 加载医生数据
        all_doctors = sorted(self.doctor_manager.get_all_doctors())
        for doctor in all_doctors:
            group = self.doctor_manager.get_doctor_group(doctor)
            self.doctors_tree.insert("", "end", text=doctor, values=(group,))

    def _on_group_select(self, event) -> None:
        """分组选择事件"""
        selection = self.groups_tree.selection()
        if selection:
            item = selection[0]
            self.selected_group = self.groups_tree.item(item, "text")

            # 显示当前选中分组的医生
            self._show_group_doctors(self.selected_group)

            # 启用分组相关按钮
            self._update_button_states()

    def _on_doctor_select(self, event) -> None:
        """医生选择事件"""
        selection = self.doctors_tree.selection()
        if selection:
            item = selection[0]
            self.selected_doctor = self.doctors_tree.item(item, "text")

            # 更新按钮状态
            self._update_button_states()
        else:
            self.selected_doctor = None
            self._update_button_states()

    def _on_doctor_double_click(self, event) -> None:
        """医生双击事件 - 快速移动分组"""
        if self.selected_doctor:
            self._move_doctor()

    def _show_group_doctors(self, group_name: str) -> None:
        """显示指定分组的医生"""
        try:
            # 清空现有数据
            for item in self.doctors_tree.get_children():
                self.doctors_tree.delete(item)

            # 更新标题
            doctors = self.doctor_manager.get_group_doctors(group_name)
            self.current_group_label.config(text=f"当前分组: {group_name} ({len(doctors)}名医生)")
            self.doctors_frame.config(text=f"医生列表 - {group_name}")

            # 加载该分组的医生
            for doctor in doctors:
                self.doctors_tree.insert("", "end", text=doctor, values=(group_name,))

            logger.info(f"显示分组 '{group_name}' 的 {len(doctors)} 名医生")

        except Exception as e:
            logger.error(f"显示分组医生失败: {str(e)}")

    def _view_all_doctors(self) -> None:
        """查看所有医生"""
        try:
            # 清空现有数据
            for item in self.doctors_tree.get_children():
                self.doctors_tree.delete(item)

            # 更新标题
            all_doctors = sorted(self.doctor_manager.get_all_doctors())
            self.current_group_label.config(text=f"显示所有医生 (共{len(all_doctors)}名)")
            self.doctors_frame.config(text="医生列表 - 所有医生")

            # 加载所有医生
            for doctor in all_doctors:
                group = self.doctor_manager.get_doctor_group(doctor)
                self.doctors_tree.insert("", "end", text=doctor, values=(group,))

            # 清空选中的分组
            self.selected_group = None
            self.groups_tree.selection_remove(self.groups_tree.selection())

            # 更新按钮状态
            self._update_button_states()

            logger.info(f"显示所有 {len(all_doctors)} 名医生")

        except Exception as e:
            logger.error(f"显示所有医生失败: {str(e)}")

    def _update_button_states(self) -> None:
        """更新按钮状态"""
        try:
            # 分组相关按钮状态
            group_selected = bool(self.selected_group)
            doctor_selected = bool(self.selected_doctor)

            # 针对当前分组的按钮
            self.add_doctor_to_group_button.config(state="normal" if group_selected else "disabled")
            self.remove_from_group_button.config(state="normal" if (group_selected and doctor_selected) else "disabled")

            # 医生操作按钮
            self.move_doctor_button.config(state="normal" if doctor_selected else "disabled")
            self.delete_doctor_button.config(state="normal" if doctor_selected else "disabled")

        except Exception as e:
            logger.error(f"更新按钮状态失败: {str(e)}")

    def _add_group(self) -> None:
        """添加新分组"""
        dialog = InputDialog(self.dialog, "新建分组", "请输入分组名称:")
        group_name = dialog.show()

        if group_name:
            if self.doctor_manager.create_group(group_name):
                self._refresh_groups()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", f"成功创建分组 '{group_name}'")
            else:
                messagebox.showerror("错误", f"创建分组失败，分组 '{group_name}' 可能已存在")

    def _rename_group(self) -> None:
        """重命名分组"""
        if not self.selected_group:
            messagebox.showwarning("提示", "请先选择要重命名的分组")
            return

        dialog = InputDialog(self.dialog, "重命名分组", "请输入新的分组名称:", self.selected_group)
        new_name = dialog.show()

        if new_name and new_name != self.selected_group:
            # 创建新分组
            if self.doctor_manager.create_group(new_name):
                # 移动所有医生到新分组
                doctors = self.doctor_manager.get_group_doctors(self.selected_group)
                for doctor in doctors:
                    self.doctor_manager.move_doctor(doctor, new_name)

                # 删除旧分组
                self.doctor_manager.delete_group(self.selected_group, new_name)

                self._refresh_groups()
                self._refresh_doctors()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", f"成功将分组 '{self.selected_group}' 重命名为 '{new_name}'")
            else:
                messagebox.showerror("错误", f"重命名失败，分组 '{new_name}' 可能已存在")

    def _delete_group(self) -> None:
        """删除分组"""
        if not self.selected_group:
            messagebox.showwarning("提示", "请先选择要删除的分组")
            return

        doctors = self.doctor_manager.get_group_doctors(self.selected_group)
        if doctors:
            message = f"分组 '{self.selected_group}' 包含 {len(doctors)} 名医生，删除后这些医生将移动到'其他组'。确认删除吗？"
        else:
            message = f"确认删除分组 '{self.selected_group}' 吗？"

        dialog = ConfirmDialog(self.dialog, "确认删除", message)
        if dialog.show():
            if self.doctor_manager.delete_group(self.selected_group, "其他组"):
                self._refresh_groups()
                self._refresh_doctors()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", f"成功删除分组 '{self.selected_group}'")
            else:
                messagebox.showerror("错误", "删除分组失败")

    def _add_doctor(self) -> None:
        """添加医生"""
        dialog = InputDialog(self.dialog, "添加医生", "请输入医生姓名:")
        doctor_name = dialog.show()

        if doctor_name:
            # 选择分组
            groups = self.doctor_manager.get_all_groups()
            if not groups:
                messagebox.showerror("错误", "没有可用的分组，请先创建分组")
                return

            # 创建分组选择对话框
            group_dialog = GroupSelectionDialog(self.dialog, groups, "选择分组")
            selected_group = group_dialog.show()

            if selected_group:
                if self.doctor_manager.add_doctor(doctor_name, selected_group):
                    self._refresh_groups()
                    self._refresh_doctors()
                    self._trigger_update()  # 立即更新主窗口
                    messagebox.showinfo("成功", f"成功添加医生 '{doctor_name}' 到 '{selected_group}'")
                else:
                    messagebox.showerror("错误", "添加医生失败")

    def _add_doctor_to_current_group(self) -> None:
        """添加医生到当前选中的分组"""
        if not self.selected_group:
            messagebox.showwarning("提示", "请先选择一个分组")
            return

        # 获取不在当前分组中的医生列表
        all_doctors = sorted(self.doctor_manager.get_all_doctors())
        current_group_doctors = set(self.doctor_manager.get_group_doctors(self.selected_group))
        available_doctors = [doctor for doctor in all_doctors if doctor not in current_group_doctors]

        if not available_doctors:
            messagebox.showinfo("提示", "所有医生都已在当前分组中")
            return

        # 创建医生选择对话框
        doctor_dialog = DoctorSelectionDialog(
            self.dialog,
            available_doctors,
            f"选择要添加到 '{self.selected_group}' 的医生"
        )
        selected_doctor = doctor_dialog.show()

        if selected_doctor:
            if self.doctor_manager.move_doctor(selected_doctor, self.selected_group):
                self._refresh_groups()
                self._show_group_doctors(self.selected_group)  # 刷新当前分组显示
                self._trigger_update()
                messagebox.showinfo("成功", f"成功添加医生 '{selected_doctor}' 到 '{self.selected_group}'")
            else:
                messagebox.showerror("错误", "添加医生失败")

    def _remove_from_current_group(self) -> None:
        """将医生从当前分组移出（移动到其他组）"""
        if not self.selected_group:
            messagebox.showwarning("提示", "请先选择一个分组")
            return

        if not self.selected_doctor:
            messagebox.showwarning("提示", "请先选择要移出的医生")
            return

        # 获取其他分组
        other_groups = [g for g in self.doctor_manager.get_all_groups() if g != self.selected_group]

        if not other_groups:
            messagebox.showwarning("提示", "没有其他分组可移动，请先创建其他分组")
            return

        # 选择目标分组
        group_dialog = GroupSelectionDialog(self.dialog, other_groups, f"将医生 '{self.selected_doctor}' 移出 '{self.selected_group}'")
        target_group = group_dialog.show()

        if target_group:
            if self.doctor_manager.move_doctor(self.selected_doctor, target_group):
                self._refresh_groups()
                self._show_group_doctors(self.selected_group)  # 刷新当前分组显示
                self._trigger_update()
                messagebox.showinfo("成功", f"成功将医生 '{self.selected_doctor}' 从 '{self.selected_group}' 移动到 '{target_group}'")
            else:
                messagebox.showerror("错误", "移动医生失败")

    def _move_doctor(self) -> None:
        """移动医生到其他分组"""
        if not self.selected_doctor:
            messagebox.showwarning("提示", "请先选择要移动的医生")
            return

        current_group = self.doctor_manager.get_doctor_group(self.selected_doctor)
        groups = [g for g in self.doctor_manager.get_all_groups() if g != current_group]

        if not groups:
            messagebox.showwarning("提示", "没有其他分组可移动")
            return

        # 创建分组选择对话框
        group_dialog = GroupSelectionDialog(self.dialog, groups, f"移动医生 '{self.selected_doctor}'")
        target_group = group_dialog.show()

        if target_group:
            if self.doctor_manager.move_doctor(self.selected_doctor, target_group):
                self._refresh_groups()
                # 如果当前在分组模式，刷新当前分组显示
                if self.selected_group:
                    self._show_group_doctors(self.selected_group)
                else:
                    self._refresh_doctors()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", f"成功将医生 '{self.selected_doctor}' 移动到 '{target_group}'")
            else:
                messagebox.showerror("错误", "移动医生失败")

    def _delete_doctor(self) -> None:
        """删除医生"""
        if not self.selected_doctor:
            messagebox.showwarning("提示", "请先选择要删除的医生")
            return

        dialog = ConfirmDialog(self.dialog, "确认删除", f"确认删除医生 '{self.selected_doctor}' 吗？")
        if dialog.show():
            if self.doctor_manager.remove_doctor(self.selected_doctor):
                self._refresh_groups()
                # 如果当前在分组模式，刷新当前分组显示
                if self.selected_group:
                    self._show_group_doctors(self.selected_group)
                else:
                    self._refresh_doctors()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", f"成功删除医生 '{self.selected_doctor}'")
            else:
                messagebox.showerror("错误", "删除医生失败")

    def _save_configuration(self) -> None:
        """保存配置"""
        # 手动保存时创建备份
        if self.doctor_manager.save_configuration(create_backup=True):
            messagebox.showinfo("成功", "配置保存成功，已创建备份")
        else:
            messagebox.showerror("错误", "配置保存失败")

    def _load_configuration(self) -> None:
        """加载配置"""
        dialog = ConfirmDialog(self.dialog, "确认加载", "加载配置将覆盖当前数据，确认继续吗？")
        if dialog.show():
            if self.doctor_manager.load_configuration():
                self._refresh_groups()
                self._refresh_doctors()
                self._trigger_update()  # 立即更新主窗口
                messagebox.showinfo("成功", "配置加载成功")
            else:
                messagebox.showerror("错误", "配置加载失败")

    def _auto_save(self) -> None:
        """自动保存配置"""
        try:
            self.doctor_manager.save_configuration()
            logger.info("自动保存配置成功")
        except Exception as e:
            logger.error(f"自动保存配置失败: {str(e)}")

    def _trigger_update(self) -> None:
        """触发主窗口更新"""
        try:
            # 立即触发更新回调
            if self.on_update_callback:
                self.on_update_callback()
        except Exception as e:
            logger.error(f"触发更新回调失败: {str(e)}")

    def _close_dialog(self) -> None:
        """关闭对话框"""
        # 触发更新回调
        if self.on_update_callback:
            self.on_update_callback()

        self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")


class GroupSelectionDialog:
    """分组选择对话框"""

    def __init__(self, parent: tk.Tk, groups: List[str], title: str = "选择分组"):
        """
        初始化分组选择对话框

        Args:
            parent: 父窗口
            groups: 分组列表
            title: 对话框标题
        """
        self.parent = parent
        self.groups = groups
        self.title = title
        self.result = None
        self.dialog = None
        self.listbox = None

    def show(self) -> Optional[str]:
        """
        显示对话框并返回选择的分组

        Returns:
            选择的分组名称，取消则返回None
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("300x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 标题标签
        title_label = ttk.Label(main_frame, text="请选择目标分组:")
        title_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 分组列表框
        listbox_frame = ttk.Frame(main_frame)
        listbox_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 20))

        self.listbox = tk.Listbox(listbox_frame, height=15)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=scrollbar.set)

        # 添加分组到列表
        for group in self.groups:
            self.listbox.insert(tk.END, group)

        # 绑定双击事件
        self.listbox.bind("<Double-1>", lambda e: self._on_confirm())

        # 布局
        self.listbox.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        listbox_frame.grid_rowconfigure(0, weight=1)
        listbox_frame.grid_columnconfigure(0, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0)

        # 确认按钮
        confirm_button = ttk.Button(button_frame, text="确认", command=self._on_confirm)
        confirm_button.pack(side="left", padx=(0, 10))

        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=self._on_cancel)
        cancel_button.pack(side="left")

        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 居中显示
        self.center_dialog()

        # 等待用户操作
        self.dialog.wait_window()

        return self.result

    def _on_confirm(self) -> None:
        """确认按钮回调"""
        selection = self.listbox.curselection()
        if selection:
            self.result = self.groups[selection[0]]
            self.dialog.destroy()
        else:
            messagebox.showwarning("提示", "请选择一个分组")

    def _on_cancel(self) -> None:
        """取消按钮回调"""
        self.result = None
        self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")


class DoctorSelectionDialog:
    """医生选择对话框"""

    def __init__(self, parent: tk.Tk, doctors: List[str], title: str = "选择医生"):
        """
        初始化医生选择对话框

        Args:
            parent: 父窗口
            doctors: 医生列表
            title: 对话框标题
        """
        self.parent = parent
        self.doctors = doctors
        self.title = title
        self.result = None
        self.dialog = None
        self.listbox = None

    def show(self) -> Optional[str]:
        """
        显示对话框并返回选择的医生

        Returns:
            选择的医生姓名，取消则返回None
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("300x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 标题标签
        title_label = ttk.Label(main_frame, text="请选择医生:")
        title_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 医生列表框
        listbox_frame = ttk.Frame(main_frame)
        listbox_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 20))

        self.listbox = tk.Listbox(listbox_frame, height=15)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=scrollbar.set)

        # 添加医生到列表
        for doctor in self.doctors:
            self.listbox.insert(tk.END, doctor)

        # 绑定双击事件
        self.listbox.bind("<Double-1>", lambda e: self._on_confirm())

        # 布局
        self.listbox.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        listbox_frame.grid_rowconfigure(0, weight=1)
        listbox_frame.grid_columnconfigure(0, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0)

        # 确认按钮
        confirm_button = ttk.Button(button_frame, text="确认", command=self._on_confirm)
        confirm_button.pack(side="left", padx=(0, 10))

        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=self._on_cancel)
        cancel_button.pack(side="left")

        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 居中显示
        self.center_dialog()

        # 等待用户操作
        self.dialog.wait_window()

        return self.result

    def _on_confirm(self) -> None:
        """确认按钮回调"""
        selection = self.listbox.curselection()
        if selection:
            self.result = self.doctors[selection[0]]
            self.dialog.destroy()
        else:
            messagebox.showwarning("提示", "请选择一个医生")

    def _on_cancel(self) -> None:
        """取消按钮回调"""
        self.result = None
        self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")