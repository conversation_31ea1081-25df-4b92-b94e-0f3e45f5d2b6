#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块
医院科室绩效计算器的主用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from typing import Optional
import logging

from ..utils.logger import get_logger
from ..core.doctor_manager import Doctor<PERSON><PERSON><PERSON>
from ..core.patient_processor import PatientProcessor
from ..core.performance_calculator import PerformanceCalculator
from ..core.report_generator import ReportGenerator
from ..utils.file_utils import validate_folder_structure
from .components import ProgressDialog, DataPreviewDialog, LogViewDialog

logger = get_logger()

class PerformanceCalculatorGUI:
    """绩效计算器主窗口"""

    def __init__(self, root: tk.Tk):
        """
        初始化主窗口

        Args:
            root: Tkinter根窗口
        """
        self.root = root

        # 首先初始化核心组件
        self.doctor_manager = DoctorManager()
        self.patient_processor = PatientProcessor()
        self.performance_calculator = PerformanceCalculator(self.doctor_manager)
        self.report_generator = ReportGenerator()

        # 数据存储
        self.selected_folder = None
        self.processed_patient_data = None
        self.calculation_results = None

        # 然后设置界面
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        self.setup_layout()

        logger.info("主窗口初始化完成")

    def setup_window(self) -> None:
        """设置窗口属性"""
        self.root.title("医院科室绩效计算器 v1.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass

        # 居中显示窗口
        self.center_window()

    def center_window(self) -> None:
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_variables(self) -> None:
        """设置变量"""
        self.folder_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()

        # 步骤状态变量
        self.step1_status = tk.StringVar(value="待执行")
        self.step2_status = tk.StringVar(value="待执行")
        self.step3_status = tk.StringVar(value="待执行")
        self.step4_status = tk.StringVar(value="待执行")

    def setup_widgets(self) -> None:
        """设置界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")

        # 标题
        self.title_label = ttk.Label(
            self.main_frame,
            text="医院科室绩效计算器",
            font=("Arial", 16, "bold")
        )

        # 步骤框架
        self.steps_frame = ttk.LabelFrame(self.main_frame, text="操作步骤", padding="10")

        # 步骤1: 选择数据文件夹
        self.step1_frame = ttk.Frame(self.steps_frame)
        self.step1_label = ttk.Label(self.step1_frame, text="1. 选择数据文件夹:")
        self.folder_entry = ttk.Entry(self.step1_frame, textvariable=self.folder_path_var, width=50)
        self.browse_button = ttk.Button(self.step1_frame, text="浏览", command=self.browse_folder)
        self.step1_status_label = ttk.Label(self.step1_frame, textvariable=self.step1_status, foreground="gray")

        # 步骤2: 处理患者数据
        self.step2_frame = ttk.Frame(self.steps_frame)
        self.step2_label = ttk.Label(self.step2_frame, text="2. 处理患者数据:")
        self.process_patients_button = ttk.Button(
            self.step2_frame,
            text="处理患者名单",
            command=self.process_patients,
            state="disabled"
        )
        self.preview_patients_button = ttk.Button(
            self.step2_frame,
            text="预览数据",
            command=self.preview_patient_data,
            state="disabled"
        )
        self.step2_status_label = ttk.Label(self.step2_frame, textvariable=self.step2_status, foreground="gray")

        # 步骤3: 计算绩效
        self.step3_frame = ttk.Frame(self.steps_frame)
        self.step3_label = ttk.Label(self.step3_frame, text="3. 计算绩效数据:")
        self.calculate_button = ttk.Button(
            self.step3_frame,
            text="开始计算",
            command=self.calculate_performance,
            state="disabled"
        )
        self.preview_results_button = ttk.Button(
            self.step3_frame,
            text="预览结果",
            command=self.preview_results,
            state="disabled"
        )
        self.step3_status_label = ttk.Label(self.step3_frame, textvariable=self.step3_status, foreground="gray")

        # 步骤4: 导出报告
        self.step4_frame = ttk.Frame(self.steps_frame)
        self.step4_label = ttk.Label(self.step4_frame, text="4. 导出报告:")
        self.export_button = ttk.Button(
            self.step4_frame,
            text="导出Excel报告",
            command=self.export_report,
            state="disabled"
        )
        self.step4_status_label = ttk.Label(self.step4_frame, textvariable=self.step4_status, foreground="gray")

        # 信息显示区域
        self.info_frame = ttk.LabelFrame(self.main_frame, text="信息显示", padding="10")

        # 医生分组信息
        self.doctor_info_frame = ttk.Frame(self.info_frame)
        self.doctor_info_label = ttk.Label(self.doctor_info_frame, text="医生分组信息:")
        self.doctor_info_text = tk.Text(
            self.doctor_info_frame,
            height=8,
            width=40,
            wrap=tk.WORD,
            state="disabled"
        )
        self.doctor_scrollbar = ttk.Scrollbar(self.doctor_info_frame, orient="vertical", command=self.doctor_info_text.yview)
        self.doctor_info_text.configure(yscrollcommand=self.doctor_scrollbar.set)

        # 处理日志
        self.log_frame = ttk.Frame(self.info_frame)
        self.log_label = ttk.Label(self.log_frame, text="处理日志:")
        self.log_text = tk.Text(
            self.log_frame,
            height=8,
            width=40,
            wrap=tk.WORD,
            state="disabled"
        )
        self.log_scrollbar = ttk.Scrollbar(self.log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=self.log_scrollbar.set)

        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.progress_bar = ttk.Progressbar(
            self.status_frame,
            variable=self.progress_var,
            maximum=100,
            length=200
        )

        # 工具按钮
        self.tools_frame = ttk.Frame(self.main_frame)
        self.manage_doctors_button = ttk.Button(self.tools_frame, text="医生管理", command=self.manage_doctors)
        self.view_log_button = ttk.Button(self.tools_frame, text="查看详细日志", command=self.view_detailed_log)
        self.create_template_button = ttk.Button(self.tools_frame, text="创建数据模板", command=self.create_data_template)
        self.about_button = ttk.Button(self.tools_frame, text="关于", command=self.show_about)

        # 初始化医生信息显示
        self.update_doctor_info()

    def setup_layout(self) -> None:
        """设置布局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # 标题
        self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 步骤框架
        self.steps_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.main_frame.grid_columnconfigure(0, weight=1)

        # 步骤1
        self.step1_frame.grid(row=0, column=0, sticky="ew", pady=5)
        self.step1_label.grid(row=0, column=0, sticky="w")
        self.folder_entry.grid(row=1, column=0, sticky="ew", padx=(0, 5))
        self.browse_button.grid(row=1, column=1)
        self.step1_status_label.grid(row=1, column=2, padx=(10, 0))
        self.step1_frame.grid_columnconfigure(0, weight=1)

        # 步骤2
        self.step2_frame.grid(row=1, column=0, sticky="ew", pady=5)
        self.step2_label.grid(row=0, column=0, sticky="w")
        self.process_patients_button.grid(row=1, column=0, sticky="w")
        self.preview_patients_button.grid(row=1, column=1, padx=(10, 0))
        self.step2_status_label.grid(row=1, column=2, padx=(10, 0))

        # 步骤3
        self.step3_frame.grid(row=2, column=0, sticky="ew", pady=5)
        self.step3_label.grid(row=0, column=0, sticky="w")
        self.calculate_button.grid(row=1, column=0, sticky="w")
        self.preview_results_button.grid(row=1, column=1, padx=(10, 0))
        self.step3_status_label.grid(row=1, column=2, padx=(10, 0))

        # 步骤4
        self.step4_frame.grid(row=3, column=0, sticky="ew", pady=5)
        self.step4_label.grid(row=0, column=0, sticky="w")
        self.export_button.grid(row=1, column=0, sticky="w")
        self.step4_status_label.grid(row=1, column=1, padx=(10, 0))

        # 信息显示区域
        self.info_frame.grid(row=2, column=0, columnspan=2, sticky="nsew", pady=(10, 0))
        self.main_frame.grid_rowconfigure(2, weight=1)

        # 医生信息和日志并排显示
        self.doctor_info_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        self.log_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        self.info_frame.grid_columnconfigure(0, weight=1)
        self.info_frame.grid_columnconfigure(1, weight=1)
        self.info_frame.grid_rowconfigure(0, weight=1)

        # 医生信息布局
        self.doctor_info_label.grid(row=0, column=0, sticky="w")
        self.doctor_info_text.grid(row=1, column=0, sticky="nsew")
        self.doctor_scrollbar.grid(row=1, column=1, sticky="ns")
        self.doctor_info_frame.grid_columnconfigure(0, weight=1)
        self.doctor_info_frame.grid_rowconfigure(1, weight=1)

        # 日志布局
        self.log_label.grid(row=0, column=0, sticky="w")
        self.log_text.grid(row=1, column=0, sticky="nsew")
        self.log_scrollbar.grid(row=1, column=1, sticky="ns")
        self.log_frame.grid_columnconfigure(0, weight=1)
        self.log_frame.grid_rowconfigure(1, weight=1)

        # 工具按钮
        self.tools_frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.manage_doctors_button.grid(row=0, column=0, padx=(0, 5))
        self.view_log_button.grid(row=0, column=1, padx=(0, 5))
        self.create_template_button.grid(row=0, column=2, padx=(0, 5))
        self.about_button.grid(row=0, column=3)

        # 状态栏
        self.status_frame.grid(row=4, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.status_label.grid(row=0, column=0, sticky="w")
        self.progress_bar.grid(row=0, column=1, padx=(10, 0))
        self.status_frame.grid_columnconfigure(0, weight=1)

    def browse_folder(self) -> None:
        """浏览并选择数据文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含数据文件的文件夹")

        if folder_path:
            self.folder_path_var.set(folder_path)
            self.selected_folder = folder_path
            self.validate_folder()

    def validate_folder(self) -> None:
        """验证选中的文件夹"""
        if not self.selected_folder:
            return

        self.update_status("正在验证文件夹...")

        try:
            is_valid, found_files, missing_files = validate_folder_structure(self.selected_folder)

            if is_valid:
                self.step1_status.set("✓ 验证通过")
                self.process_patients_button.config(state="normal")
                self.log_message(f"文件夹验证成功，找到所有必需文件")
                self.log_message(f"找到文件: {', '.join(found_files)}")
            else:
                self.step1_status.set("✗ 验证失败")
                self.process_patients_button.config(state="disabled")
                self.log_message(f"文件夹验证失败，缺少文件: {', '.join(missing_files)}")
                messagebox.showwarning(
                    "文件夹验证失败",
                    f"缺少以下必需文件:\n{chr(10).join(missing_files)}\n\n请确保文件夹包含所有必需的数据文件。"
                )

            self.update_status("就绪")

        except Exception as e:
            self.step1_status.set("✗ 验证错误")
            self.log_message(f"文件夹验证出错: {str(e)}")
            self.update_status("就绪")
            messagebox.showerror("验证错误", f"验证文件夹时发生错误:\n{str(e)}")

    def process_patients(self) -> None:
        """处理患者数据"""
        if not self.selected_folder:
            messagebox.showwarning("警告", "请先选择数据文件夹")
            return

        def process_task():
            try:
                self.update_status("正在处理患者数据...")
                self.set_progress(10)

                # 加载患者数据
                patients_file = os.path.join(self.selected_folder, "patients.xlsx")
                if not self.patient_processor.load_patient_data(patients_file):
                    raise Exception("加载患者数据失败")

                self.set_progress(30)

                # 分析数据结构
                analysis = self.patient_processor.analyze_data_structure()
                self.log_message(f"患者数据分析: {analysis['total_records']} 条记录, {analysis['column_count']} 列")

                self.set_progress(50)

                # 检测重复
                duplicate_info = self.patient_processor.detect_duplicates()
                if duplicate_info.get("total_duplicates", 0) > 0:
                    self.log_message(f"发现 {duplicate_info['total_duplicates']} 条重复记录")

                    # 询问用户是否去重
                    result = messagebox.askyesno(
                        "发现重复数据",
                        f"发现 {duplicate_info['total_duplicates']} 条重复记录。\n是否自动去重（保留最新记录）？"
                    )

                    if result:
                        if not self.patient_processor.remove_duplicates("keep_last"):
                            raise Exception("去重处理失败")
                        self.log_message("重复数据去重完成")

                self.set_progress(80)

                # 获取处理后的数据
                self.processed_patient_data = self.patient_processor.get_cleaned_data()
                if self.processed_patient_data is None:
                    self.processed_patient_data = self.patient_processor.original_data

                self.set_progress(100)

                # 更新界面状态
                self.root.after(0, self._on_patients_processed_success)

            except Exception as e:
                logger.error(f"处理患者数据失败: {str(e)}")
                self.root.after(0, lambda: self._on_patients_processed_error(str(e)))

        # 在后台线程中执行
        threading.Thread(target=process_task, daemon=True).start()

    def _on_patients_processed_success(self) -> None:
        """患者数据处理成功的回调"""
        self.step2_status.set("✓ 处理完成")
        self.preview_patients_button.config(state="normal")
        self.calculate_button.config(state="normal")
        self.log_message(f"患者数据处理完成，共 {len(self.processed_patient_data)} 条记录")
        self.update_status("患者数据处理完成")
        self.set_progress(0)

    def _on_patients_processed_error(self, error_msg: str) -> None:
        """患者数据处理失败的回调"""
        self.step2_status.set("✗ 处理失败")
        self.log_message(f"患者数据处理失败: {error_msg}")
        self.update_status("就绪")
        self.set_progress(0)
        messagebox.showerror("处理失败", f"处理患者数据时发生错误:\n{error_msg}")

    def preview_patient_data(self) -> None:
        """预览患者数据"""
        if self.processed_patient_data is None:
            messagebox.showwarning("警告", "请先处理患者数据")
            return

        try:
            dialog = DataPreviewDialog(self.root, self.processed_patient_data, "患者数据预览")
            dialog.show()
        except Exception as e:
            messagebox.showerror("预览错误", f"预览数据时发生错误:\n{str(e)}")

    def calculate_performance(self) -> None:
        """计算绩效数据"""
        if self.processed_patient_data is None:
            messagebox.showwarning("警告", "请先处理患者数据")
            return

        def calculate_task():
            try:
                self.update_status("正在计算绩效数据...")
                self.set_progress(10)

                # 加载患者数据到计算器
                if not self.performance_calculator.load_patient_data(self.processed_patient_data):
                    raise Exception("加载患者数据到计算器失败")

                self.set_progress(20)

                # 加载绩效数据
                load_results = self.performance_calculator.load_performance_data(self.selected_folder)
                successful_loads = sum(1 for success in load_results.values() if success)

                if successful_loads == 0:
                    raise Exception("没有成功加载任何绩效数据文件")

                self.log_message(f"成功加载 {successful_loads}/{len(load_results)} 个绩效数据文件")
                self.set_progress(50)

                # 执行绩效计算
                if not self.performance_calculator.calculate_performance():
                    raise Exception("绩效计算失败")

                self.set_progress(80)

                # 生成汇总数据
                individual_summary = self.performance_calculator.get_individual_summary()
                group_summary = self.performance_calculator.get_group_summary()
                detailed_results = self.performance_calculator.get_detailed_results()

                self.calculation_results = {
                    "individual_summary": individual_summary,
                    "group_summary": group_summary,
                    "detailed_results": detailed_results,
                    "processing_log": self.performance_calculator.get_processing_log()
                }

                self.set_progress(100)

                # 更新界面状态
                self.root.after(0, self._on_calculation_success)

            except Exception as e:
                logger.error(f"绩效计算失败: {str(e)}")
                self.root.after(0, lambda: self._on_calculation_error(str(e)))

        # 在后台线程中执行
        threading.Thread(target=calculate_task, daemon=True).start()

    def _on_calculation_success(self) -> None:
        """绩效计算成功的回调"""
        self.step3_status.set("✓ 计算完成")
        self.preview_results_button.config(state="normal")
        self.export_button.config(state="normal")

        if self.calculation_results["detailed_results"] is not None:
            record_count = len(self.calculation_results["detailed_results"])
            self.log_message(f"绩效计算完成，生成 {record_count} 条结果记录")

        self.update_status("绩效计算完成")
        self.set_progress(0)

    def _on_calculation_error(self, error_msg: str) -> None:
        """绩效计算失败的回调"""
        self.step3_status.set("✗ 计算失败")
        self.log_message(f"绩效计算失败: {error_msg}")
        self.update_status("就绪")
        self.set_progress(0)
        messagebox.showerror("计算失败", f"计算绩效数据时发生错误:\n{error_msg}")

    def preview_results(self) -> None:
        """预览计算结果"""
        if not self.calculation_results:
            messagebox.showwarning("警告", "请先计算绩效数据")
            return

        try:
            # 显示详细结果预览
            detailed_results = self.calculation_results["detailed_results"]
            if detailed_results is not None and not detailed_results.empty:
                dialog = DataPreviewDialog(self.root, detailed_results, "绩效计算结果预览")
                dialog.show()
            else:
                messagebox.showinfo("信息", "没有计算结果可预览")
        except Exception as e:
            messagebox.showerror("预览错误", f"预览结果时发生错误:\n{str(e)}")

    def export_report(self) -> None:
        """导出Excel报告"""
        if not self.calculation_results:
            messagebox.showwarning("警告", "请先计算绩效数据")
            return

        def export_task():
            try:
                self.update_status("正在生成Excel报告...")
                self.set_progress(20)

                # 生成报告
                output_path = self.report_generator.generate_excel_report(
                    detailed_results=self.calculation_results["detailed_results"],
                    individual_summary=self.calculation_results["individual_summary"],
                    group_summary=self.calculation_results["group_summary"],
                    processing_log=self.calculation_results["processing_log"],
                    doctor_manager=self.doctor_manager
                )

                self.set_progress(100)

                if output_path:
                    self.root.after(0, lambda: self._on_export_success(output_path))
                else:
                    self.root.after(0, lambda: self._on_export_error("报告生成失败"))

            except Exception as e:
                logger.error(f"导出报告失败: {str(e)}")
                self.root.after(0, lambda: self._on_export_error(str(e)))

        # 在后台线程中执行
        threading.Thread(target=export_task, daemon=True).start()

    def _on_export_success(self, output_path: str) -> None:
        """报告导出成功的回调"""
        self.step4_status.set("✓ 导出完成")
        self.log_message(f"Excel报告导出成功: {output_path}")
        self.update_status("报告导出完成")
        self.set_progress(0)

        # 询问是否打开文件
        result = messagebox.askyesno(
            "导出成功",
            f"Excel报告已成功导出到:\n{output_path}\n\n是否现在打开文件？"
        )

        if result:
            try:
                os.startfile(output_path)  # Windows
            except AttributeError:
                try:
                    os.system(f"open '{output_path}'")  # macOS
                except:
                    os.system(f"xdg-open '{output_path}'")  # Linux
            except Exception as e:
                messagebox.showwarning("打开文件失败", f"无法打开文件:\n{str(e)}")

    def _on_export_error(self, error_msg: str) -> None:
        """报告导出失败的回调"""
        self.step4_status.set("✗ 导出失败")
        self.log_message(f"Excel报告导出失败: {error_msg}")
        self.update_status("就绪")
        self.set_progress(0)
        messagebox.showerror("导出失败", f"导出Excel报告时发生错误:\n{error_msg}")

    def view_detailed_log(self) -> None:
        """查看详细日志"""
        try:
            # 收集所有日志
            all_logs = []

            # 患者处理日志
            if hasattr(self.patient_processor, 'processing_log'):
                all_logs.extend(self.patient_processor.processing_log)

            # 绩效计算日志
            if hasattr(self.performance_calculator, 'processing_log'):
                all_logs.extend(self.performance_calculator.processing_log)

            # 报告生成日志
            if hasattr(self.report_generator, 'generation_log'):
                all_logs.extend(self.report_generator.generation_log)

            if all_logs:
                dialog = LogViewDialog(self.root, all_logs, "详细处理日志")
                dialog.show()
            else:
                messagebox.showinfo("信息", "暂无详细日志")

        except Exception as e:
            messagebox.showerror("日志查看错误", f"查看日志时发生错误:\n{str(e)}")

    def create_data_template(self) -> None:
        """创建数据模板"""
        try:
            output_dir = filedialog.askdirectory(title="选择模板保存位置")
            if not output_dir:
                return

            self.update_status("正在创建数据模板...")

            template_dir = self.report_generator.create_sample_data_template(output_dir)
            if template_dir:
                self.log_message(f"数据模板创建成功: {template_dir}")
                messagebox.showinfo(
                    "模板创建成功",
                    f"数据模板已创建到:\n{template_dir}\n\n包含以下文件:\n- patients_template.xlsx\n- 1.csv\n- 2.csv\n- 3.csv\n- icu.csv"
                )
            else:
                messagebox.showerror("创建失败", "创建数据模板失败")

            self.update_status("就绪")

        except Exception as e:
            self.log_message(f"创建数据模板失败: {str(e)}")
            messagebox.showerror("创建失败", f"创建数据模板时发生错误:\n{str(e)}")
            self.update_status("就绪")

    def show_about(self) -> None:
        """显示关于信息"""
        about_text = """医院科室绩效计算器 v1.0

基于原VBA代码重构的现代化Python版本

主要功能:
• 患者名单去重处理
• 多数据源绩效计算
• 医生分组管理
• Excel报告生成

技术栈:
• Python 3.x
• tkinter (GUI)
• pandas (数据处理)
• openpyxl (Excel操作)

开发: AI Assistant
版本: 1.0.0
日期: 2025-05-24"""

        messagebox.showinfo("关于", about_text)

    def manage_doctors(self) -> None:
        """打开医生管理对话框"""
        try:
            from .doctor_management_dialog import DoctorManagementDialog

            dialog = DoctorManagementDialog(
                parent=self.root,
                doctor_manager=self.doctor_manager,
                on_update_callback=self.update_doctor_info
            )
            dialog.show()

        except Exception as e:
            logger.error(f"打开医生管理对话框失败: {str(e)}")
            messagebox.showerror("错误", f"打开医生管理对话框时发生错误:\n{str(e)}")

    def update_doctor_info(self) -> None:
        """更新医生分组信息显示"""
        try:
            self.doctor_info_text.config(state="normal")
            self.doctor_info_text.delete(1.0, tk.END)

            # 显示医生分组统计
            group_stats = self.doctor_manager.get_group_statistics()
            total_doctors = sum(group_stats.values())

            info_text = f"医生分组信息 (共 {total_doctors} 名医生):\n\n"

            for group_name, count in group_stats.items():
                info_text += f"【{group_name}】({count}人)\n"
                doctors = self.doctor_manager.get_group_doctors(group_name)
                for doctor in doctors:
                    info_text += f"  • {doctor}\n"
                info_text += "\n"

            self.doctor_info_text.insert(1.0, info_text)
            self.doctor_info_text.config(state="disabled")

        except Exception as e:
            logger.error(f"更新医生信息显示失败: {str(e)}")

    def log_message(self, message: str) -> None:
        """在日志区域显示消息"""
        try:
            self.log_text.config(state="normal")

            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)  # 滚动到最新消息

            # 限制日志行数，避免内存占用过多
            lines = self.log_text.get(1.0, tk.END).split('\n')
            if len(lines) > 100:  # 保留最新100行
                self.log_text.delete(1.0, f"{len(lines)-100}.0")

            self.log_text.config(state="disabled")

        except Exception as e:
            logger.error(f"显示日志消息失败: {str(e)}")

    def update_status(self, status: str) -> None:
        """更新状态栏"""
        self.status_var.set(status)
        self.root.update_idletasks()

    def set_progress(self, value: float) -> None:
        """设置进度条"""
        self.progress_var.set(value)
        self.root.update_idletasks()
