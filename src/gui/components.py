#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件模块
提供各种对话框和自定义组件
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd
from typing import List, Dict, Optional

class DataPreviewDialog:
    """数据预览对话框"""

    def __init__(self, parent: tk.Tk, data: pd.DataFrame, title: str = "数据预览"):
        """
        初始化数据预览对话框

        Args:
            parent: 父窗口
            data: 要预览的DataFrame
            title: 对话框标题
        """
        self.parent = parent
        self.data = data
        self.title = title
        self.dialog = None

    def show(self) -> None:
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("800x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 信息标签
        info_label = ttk.Label(
            main_frame,
            text=f"数据形状: {self.data.shape[0]} 行 × {self.data.shape[1]} 列"
        )
        info_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 创建Treeview
        tree_frame = ttk.Frame(main_frame)
        tree_frame.grid(row=1, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical")
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal")

        # 创建Treeview组件
        columns = list(self.data.columns)
        tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            yscrollcommand=v_scrollbar.set,
            xscrollcommand=h_scrollbar.set
        )

        # 配置滚动条
        v_scrollbar.config(command=tree.yview)
        h_scrollbar.config(command=tree.xview)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, minwidth=50)

        # 插入数据（限制显示前1000行）
        display_data = self.data.head(1000)
        for index, row in display_data.iterrows():
            values = [str(value) if pd.notna(value) else "" for value in row]
            tree.insert("", "end", values=values)

        # 布局
        tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))

        # 关闭按钮
        close_button = ttk.Button(
            button_frame,
            text="关闭",
            command=self.dialog.destroy
        )
        close_button.pack(side="right")

        # 如果数据超过1000行，显示提示
        if len(self.data) > 1000:
            limit_label = ttk.Label(
                button_frame,
                text=f"注意: 仅显示前1000行数据（总共{len(self.data)}行）",
                foreground="orange"
            )
            limit_label.pack(side="left")

        # 居中显示
        self.center_dialog()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

class LogViewDialog:
    """日志查看对话框"""

    def __init__(self, parent: tk.Tk, log_data: List[Dict], title: str = "日志查看"):
        """
        初始化日志查看对话框

        Args:
            parent: 父窗口
            log_data: 日志数据列表
            title: 对话框标题
        """
        self.parent = parent
        self.log_data = log_data
        self.title = title
        self.dialog = None

    def show(self) -> None:
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("700x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 信息标签
        info_label = ttk.Label(
            main_frame,
            text=f"日志条目: {len(self.log_data)} 条"
        )
        info_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 创建文本框和滚动条
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=1, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        text_widget = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # 插入日志数据
        for log_entry in self.log_data:
            timestamp = log_entry.get("timestamp", "")
            step = log_entry.get("step", "")
            message = log_entry.get("message", "")

            log_line = f"[{timestamp}] [{step}] {message}\n"
            text_widget.insert(tk.END, log_line)

        # 布局
        text_widget.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        # 设置为只读
        text_widget.config(state="disabled")

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))

        # 清空按钮
        clear_button = ttk.Button(
            button_frame,
            text="清空",
            command=lambda: self.clear_log(text_widget)
        )
        clear_button.pack(side="left")

        # 关闭按钮
        close_button = ttk.Button(
            button_frame,
            text="关闭",
            command=self.dialog.destroy
        )
        close_button.pack(side="right")

        # 居中显示
        self.center_dialog()

    def clear_log(self, text_widget: tk.Text) -> None:
        """清空日志显示"""
        text_widget.config(state="normal")
        text_widget.delete(1.0, tk.END)
        text_widget.config(state="disabled")

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

class ProgressDialog:
    """进度对话框"""

    def __init__(self, parent: tk.Tk, title: str = "处理中..."):
        """
        初始化进度对话框

        Args:
            parent: 父窗口
            title: 对话框标题
        """
        self.parent = parent
        self.title = title
        self.dialog = None
        self.progress_var = None
        self.status_var = None
        self.progress_bar = None

    def show(self) -> None:
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x150")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 状态标签
        self.status_var = tk.StringVar(value="正在处理...")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky="ew", pady=(0, 10))

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            length=300
        )
        self.progress_bar.grid(row=1, column=0, sticky="ew", pady=(0, 10))

        # 百分比标签
        self.percent_var = tk.StringVar(value="0%")
        percent_label = ttk.Label(main_frame, textvariable=self.percent_var)
        percent_label.grid(row=2, column=0)

        main_frame.grid_columnconfigure(0, weight=1)

        # 居中显示
        self.center_dialog()

    def update_progress(self, value: float, status: str = None) -> None:
        """
        更新进度

        Args:
            value: 进度值 (0-100)
            status: 状态文本
        """
        if self.progress_var:
            self.progress_var.set(value)

        if self.percent_var:
            self.percent_var.set(f"{value:.1f}%")

        if status and self.status_var:
            self.status_var.set(status)

        if self.dialog:
            self.dialog.update_idletasks()

    def close(self) -> None:
        """关闭对话框"""
        if self.dialog:
            self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

class ConfirmDialog:
    """确认对话框"""

    def __init__(self, parent: tk.Tk, title: str = "确认", message: str = "确认执行此操作吗？"):
        """
        初始化确认对话框

        Args:
            parent: 父窗口
            title: 对话框标题
            message: 确认消息
        """
        self.parent = parent
        self.title = title
        self.message = message
        self.result = False
        self.dialog = None

    def show(self) -> bool:
        """
        显示对话框并返回用户选择

        Returns:
            True表示确认，False表示取消
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("350x150")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 消息标签
        message_label = ttk.Label(
            main_frame,
            text=self.message,
            wraplength=300,
            justify="center"
        )
        message_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2)

        # 确认按钮
        confirm_button = ttk.Button(
            button_frame,
            text="确认",
            command=self._on_confirm
        )
        confirm_button.pack(side="left", padx=(0, 10))

        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self._on_cancel
        )
        cancel_button.pack(side="left")

        # 居中显示
        self.center_dialog()

        # 等待用户操作
        self.dialog.wait_window()

        return self.result

    def _on_confirm(self) -> None:
        """确认按钮回调"""
        self.result = True
        self.dialog.destroy()

    def _on_cancel(self) -> None:
        """取消按钮回调"""
        self.result = False
        self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

class InputDialog:
    """输入对话框"""

    def __init__(self, parent: tk.Tk, title: str = "输入", message: str = "请输入:", default_value: str = ""):
        """
        初始化输入对话框

        Args:
            parent: 父窗口
            title: 对话框标题
            message: 提示消息
            default_value: 默认值
        """
        self.parent = parent
        self.title = title
        self.message = message
        self.default_value = default_value
        self.result = None
        self.dialog = None
        self.entry = None

    def show(self) -> Optional[str]:
        """
        显示对话框并返回用户输入

        Returns:
            用户输入的字符串，取消则返回None
        """
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x150")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)

        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # 消息标签
        message_label = ttk.Label(main_frame, text=self.message)
        message_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 输入框
        self.entry = ttk.Entry(main_frame, width=40)
        self.entry.grid(row=1, column=0, sticky="ew", pady=(0, 20))
        self.entry.insert(0, self.default_value)
        self.entry.select_range(0, tk.END)
        self.entry.focus()

        # 绑定回车键
        self.entry.bind('<Return>', lambda e: self._on_confirm())

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0)

        # 确认按钮
        confirm_button = ttk.Button(
            button_frame,
            text="确认",
            command=self._on_confirm
        )
        confirm_button.pack(side="left", padx=(0, 10))

        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self._on_cancel
        )
        cancel_button.pack(side="left")

        main_frame.grid_columnconfigure(0, weight=1)

        # 居中显示
        self.center_dialog()

        # 等待用户操作
        self.dialog.wait_window()

        return self.result

    def _on_confirm(self) -> None:
        """确认按钮回调"""
        self.result = self.entry.get().strip()
        self.dialog.destroy()

    def _on_cancel(self) -> None:
        """取消按钮回调"""
        self.result = None
        self.dialog.destroy()

    def center_dialog(self) -> None:
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")