#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医生分组管理模块
负责解析医生名单并管理医生分组信息
"""

import os
import json
from typing import Dict, List, Set
from datetime import datetime

from ..utils.logger import get_logger

logger = get_logger()

class DoctorManager:
    """医生分组管理器"""

    def __init__(self, doctor_list_file: str = "医生名单.md", config_file: str = "医生分组配置.json"):
        """
        初始化医生管理器

        Args:
            doctor_list_file: 医生名单文件路径
            config_file: 配置文件路径
        """
        self.doctor_list_file = doctor_list_file
        self.config_file = config_file
        self.doctor_groups = {}  # 医生姓名 -> 医疗组
        self.group_doctors = {}  # 医疗组 -> 医生列表
        self.all_doctors = set()  # 所有医生姓名

        # 基于原VBA代码的医生分组规则
        self.default_groups = {
            "赖红琳组": ["赖红琳", "李凡", "陈小永"],
            "吴西雅组": ["廖丽军", "吴西雅", "吴海凤"],
            "童波组": ["童波", "刘娜", "唐斌"],
            "夏顺生组": ["梁莹", "夏顺生", "陈卫群"],
            "邹国明组": ["邹国明", "周洪"],
            "其他组": ["郭玲玲", "李星", "张琦", "黄颖", "欧阳国泉", "郭猷殚"]
        }

        self._load_doctor_groups()

    def _load_doctor_groups(self) -> None:
        """加载医生分组信息"""
        try:
            # 优先尝试加载JSON配置文件
            if os.path.exists(self.config_file):
                logger.info(f"发现配置文件: {self.config_file}，正在加载...")
                if self.load_configuration(self.config_file):
                    logger.info("从配置文件加载医生分组成功")
                    return
                else:
                    logger.warning("配置文件加载失败，使用默认分组")

            # 如果配置文件不存在，使用默认分组
            logger.info("配置文件不存在，使用默认分组")
            self._setup_default_groups()

            # 如果存在医生名单文件，则从文件中加载额外的医生
            if os.path.exists(self.doctor_list_file):
                self._load_from_file()
            else:
                logger.warning(f"医生名单文件不存在: {self.doctor_list_file}")

            # 首次启动时自动保存配置
            self.save_configuration(self.config_file)
            logger.info("首次启动，已自动保存配置")

        except Exception as e:
            logger.error(f"加载医生分组失败: {str(e)}")
            self._setup_default_groups()

    def _setup_default_groups(self) -> None:
        """设置默认医生分组"""
        logger.info("设置默认医生分组")

        for group_name, doctors in self.default_groups.items():
            self.group_doctors[group_name] = doctors.copy()
            for doctor in doctors:
                self.doctor_groups[doctor] = group_name
                self.all_doctors.add(doctor)

        logger.info(f"默认分组设置完成，共 {len(self.all_doctors)} 名医生")

    def _load_from_file(self) -> None:
        """从文件加载医生名单"""
        try:
            logger.info(f"从文件加载医生名单: {self.doctor_list_file}")

            with open(self.doctor_list_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析医生名单
            new_doctors = set()
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和注释
                    doctor_name = line.strip()
                    if doctor_name:
                        new_doctors.add(doctor_name)

            logger.info(f"从文件中读取到 {len(new_doctors)} 名医生")

            # 将新医生分配到现有分组或其他组
            for doctor in new_doctors:
                if doctor not in self.doctor_groups:
                    # 新医生默认分配到其他组
                    self.doctor_groups[doctor] = "其他组"
                    if "其他组" not in self.group_doctors:
                        self.group_doctors["其他组"] = []
                    if doctor not in self.group_doctors["其他组"]:
                        self.group_doctors["其他组"].append(doctor)

                self.all_doctors.add(doctor)

            logger.info(f"医生分组加载完成，总计 {len(self.all_doctors)} 名医生")

        except Exception as e:
            logger.error(f"从文件加载医生名单失败: {str(e)}")

    def get_doctor_group(self, doctor_name: str) -> str:
        """
        获取医生所属的医疗组

        Args:
            doctor_name: 医生姓名

        Returns:
            医疗组名称
        """
        if not doctor_name or not doctor_name.strip():
            return "未知组"

        doctor_name = doctor_name.strip()
        group = self.doctor_groups.get(doctor_name, "其他组")

        if group == "其他组" and doctor_name not in self.all_doctors:
            logger.warning(f"未知医生: {doctor_name}，分配到其他组")
            # 动态添加到其他组
            self.doctor_groups[doctor_name] = "其他组"
            self.all_doctors.add(doctor_name)
            if "其他组" not in self.group_doctors:
                self.group_doctors["其他组"] = []
            if doctor_name not in self.group_doctors["其他组"]:
                self.group_doctors["其他组"].append(doctor_name)

        return group

    def get_group_doctors(self, group_name: str) -> List[str]:
        """
        获取指定医疗组的医生列表

        Args:
            group_name: 医疗组名称

        Returns:
            医生姓名列表
        """
        return self.group_doctors.get(group_name, [])

    def get_all_groups(self) -> List[str]:
        """
        获取所有医疗组名称

        Returns:
            医疗组名称列表
        """
        return list(self.group_doctors.keys())

    def get_all_doctors(self) -> Set[str]:
        """
        获取所有医生姓名

        Returns:
            医生姓名集合
        """
        return self.all_doctors.copy()

    def get_group_statistics(self) -> Dict[str, int]:
        """
        获取各医疗组的医生数量统计

        Returns:
            医疗组统计信息
        """
        stats = {}
        for group_name, doctors in self.group_doctors.items():
            stats[group_name] = len(doctors)
        return stats

    def add_doctor(self, doctor_name: str, group_name: str = "其他组") -> bool:
        """
        添加新医生

        Args:
            doctor_name: 医生姓名
            group_name: 医疗组名称

        Returns:
            是否添加成功
        """
        try:
            doctor_name = doctor_name.strip()
            if not doctor_name:
                return False

            # 如果医生已存在，先从原分组中移除
            if doctor_name in self.doctor_groups:
                old_group = self.doctor_groups[doctor_name]
                if old_group in self.group_doctors and doctor_name in self.group_doctors[old_group]:
                    self.group_doctors[old_group].remove(doctor_name)

            # 添加到指定组
            self.doctor_groups[doctor_name] = group_name
            self.all_doctors.add(doctor_name)

            if group_name not in self.group_doctors:
                self.group_doctors[group_name] = []

            if doctor_name not in self.group_doctors[group_name]:
                self.group_doctors[group_name].append(doctor_name)

            logger.info(f"添加医生: {doctor_name} -> {group_name}")

            # 自动保存配置
            self.save_configuration()
            return True

        except Exception as e:
            logger.error(f"添加医生失败: {str(e)}")
            return False

    def remove_doctor(self, doctor_name: str) -> bool:
        """
        删除医生

        Args:
            doctor_name: 医生姓名

        Returns:
            是否删除成功
        """
        try:
            doctor_name = doctor_name.strip()
            if not doctor_name or doctor_name not in self.all_doctors:
                return False

            # 从分组中删除
            group_name = self.doctor_groups.get(doctor_name)
            if group_name and group_name in self.group_doctors:
                if doctor_name in self.group_doctors[group_name]:
                    self.group_doctors[group_name].remove(doctor_name)

            # 从映射中删除
            if doctor_name in self.doctor_groups:
                del self.doctor_groups[doctor_name]

            # 从所有医生集合中删除
            self.all_doctors.discard(doctor_name)

            logger.info(f"删除医生: {doctor_name}")

            # 自动保存配置
            self.save_configuration()
            return True

        except Exception as e:
            logger.error(f"删除医生失败: {str(e)}")
            return False

    def move_doctor(self, doctor_name: str, target_group: str) -> bool:
        """
        移动医生到指定分组

        Args:
            doctor_name: 医生姓名
            target_group: 目标分组名称

        Returns:
            是否移动成功
        """
        try:
            doctor_name = doctor_name.strip()
            target_group = target_group.strip()

            if not doctor_name or not target_group:
                return False

            if doctor_name not in self.all_doctors:
                logger.warning(f"医生 {doctor_name} 不存在")
                return False

            # 获取当前分组
            current_group = self.doctor_groups.get(doctor_name)
            if current_group == target_group:
                logger.info(f"医生 {doctor_name} 已在目标分组 {target_group} 中")
                return True

            # 从当前分组中移除
            if current_group and current_group in self.group_doctors:
                if doctor_name in self.group_doctors[current_group]:
                    self.group_doctors[current_group].remove(doctor_name)

            # 添加到目标分组
            if target_group not in self.group_doctors:
                self.group_doctors[target_group] = []

            if doctor_name not in self.group_doctors[target_group]:
                self.group_doctors[target_group].append(doctor_name)

            # 更新映射
            self.doctor_groups[doctor_name] = target_group

            logger.info(f"移动医生: {doctor_name} 从 {current_group} 到 {target_group}")

            # 自动保存配置
            self.save_configuration()
            return True

        except Exception as e:
            logger.error(f"移动医生失败: {str(e)}")
            return False

    def create_group(self, group_name: str) -> bool:
        """
        创建新分组

        Args:
            group_name: 分组名称

        Returns:
            是否创建成功
        """
        try:
            group_name = group_name.strip()
            if not group_name:
                return False

            if group_name in self.group_doctors:
                logger.warning(f"分组 {group_name} 已存在")
                return False

            self.group_doctors[group_name] = []
            logger.info(f"创建分组: {group_name}")

            # 自动保存配置
            self.save_configuration()
            return True

        except Exception as e:
            logger.error(f"创建分组失败: {str(e)}")
            return False

    def delete_group(self, group_name: str, move_to_group: str = "其他组") -> bool:
        """
        删除分组，并将医生移动到指定分组

        Args:
            group_name: 要删除的分组名称
            move_to_group: 医生要移动到的分组

        Returns:
            是否删除成功
        """
        try:
            group_name = group_name.strip()
            move_to_group = move_to_group.strip()

            if not group_name or group_name not in self.group_doctors:
                return False

            # 获取分组中的医生
            doctors = self.group_doctors[group_name].copy()

            # 移动医生到目标分组
            if doctors:
                if move_to_group not in self.group_doctors:
                    self.group_doctors[move_to_group] = []

                for doctor in doctors:
                    self.doctor_groups[doctor] = move_to_group
                    if doctor not in self.group_doctors[move_to_group]:
                        self.group_doctors[move_to_group].append(doctor)

            # 删除分组
            del self.group_doctors[group_name]

            logger.info(f"删除分组: {group_name}，移动 {len(doctors)} 名医生到 {move_to_group}")

            # 自动保存配置
            self.save_configuration()
            return True

        except Exception as e:
            logger.error(f"删除分组失败: {str(e)}")
            return False

    def save_configuration(self, config_file: str = None, create_backup: bool = False) -> bool:
        """
        保存医生分组配置到文件

        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
            create_backup: 是否创建备份文件

        Returns:
            是否保存成功
        """
        try:
            if config_file is None:
                config_file = self.config_file

            # 只在明确要求时创建备份
            if create_backup and os.path.exists(config_file):
                backup_file = f"医生分组配置_备份_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                import shutil
                shutil.copy2(config_file, backup_file)
                logger.info(f"已创建备份: {backup_file}")

                # 清理旧备份文件，只保留最近的5个
                self._cleanup_old_backups()

            # 保存配置
            config_data = {
                "timestamp": datetime.now().isoformat(),
                "total_doctors": len(self.all_doctors),
                "total_groups": len(self.group_doctors),
                "doctor_groups": self.doctor_groups,
                "group_doctors": {k: list(v) for k, v in self.group_doctors.items()}  # 确保列表可序列化
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            logger.info(f"配置已保存到: {config_file}")
            logger.info(f"保存统计: {config_data['total_groups']} 个分组, {config_data['total_doctors']} 名医生")
            return True

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return False

    def _cleanup_old_backups(self, max_backups: int = 5) -> None:
        """
        清理旧的备份文件，只保留最近的几个

        Args:
            max_backups: 最大保留的备份文件数量
        """
        try:
            import glob

            # 查找所有备份文件
            backup_pattern = "医生分组配置_备份_*.json"
            backup_files = glob.glob(backup_pattern)

            if len(backup_files) > max_backups:
                # 按修改时间排序，最新的在前
                backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

                # 删除多余的备份文件
                files_to_delete = backup_files[max_backups:]
                for file_path in files_to_delete:
                    try:
                        os.remove(file_path)
                        logger.info(f"删除旧备份文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除备份文件失败 {file_path}: {str(e)}")

        except Exception as e:
            logger.warning(f"清理备份文件失败: {str(e)}")

    def load_configuration(self, config_file: str = None) -> bool:
        """
        从文件加载医生分组配置

        Args:
            config_file: 配置文件路径，如果为None则使用默认路径

        Returns:
            是否加载成功
        """
        try:
            if config_file is None:
                config_file = self.config_file

            if not os.path.exists(config_file):
                logger.warning(f"配置文件不存在: {config_file}")
                return False

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 加载配置
            self.doctor_groups = config_data.get('doctor_groups', {})
            self.group_doctors = config_data.get('group_doctors', {})
            self.all_doctors = set(self.doctor_groups.keys())

            logger.info(f"配置加载成功: {config_data.get('total_groups', 0)} 个分组, {config_data.get('total_doctors', 0)} 名医生")
            return True

        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return False
