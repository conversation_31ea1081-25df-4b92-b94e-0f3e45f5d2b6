#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
患者数据处理模块
负责患者名单的去重和数据清理
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging

from ..utils.logger import get_logger
from ..utils.file_utils import safe_read_excel, safe_write_excel

logger = get_logger()

class PatientProcessor:
    """患者数据处理器"""

    def __init__(self):
        """初始化患者数据处理器"""
        self.original_data = None
        self.cleaned_data = None
        self.duplicate_info = {}
        self.processing_log = []

    def load_patient_data(self, file_path: str) -> bool:
        """
        加载患者数据

        Args:
            file_path: 患者数据文件路径

        Returns:
            是否加载成功
        """
        try:
            logger.info(f"正在加载患者数据: {file_path}")

            self.original_data = safe_read_excel(file_path)
            if self.original_data is None:
                return False

            logger.info(f"患者数据加载成功，原始记录数: {len(self.original_data)}")
            self._log_processing("数据加载", f"成功加载 {len(self.original_data)} 条记录")

            return True

        except Exception as e:
            logger.error(f"加载患者数据失败: {str(e)}")
            self._log_processing("数据加载", f"失败: {str(e)}")
            return False

    def analyze_data_structure(self) -> Dict[str, any]:
        """
        分析数据结构

        Returns:
            数据结构分析结果
        """
        if self.original_data is None:
            return {"error": "未加载数据"}

        analysis = {
            "total_records": len(self.original_data),
            "columns": list(self.original_data.columns),
            "column_count": len(self.original_data.columns),
            "data_types": self.original_data.dtypes.to_dict(),
            "missing_values": self.original_data.isnull().sum().to_dict(),
            "sample_data": self.original_data.head().to_dict('records') if len(self.original_data) > 0 else []
        }

        logger.info(f"数据结构分析完成: {analysis['column_count']} 列, {analysis['total_records']} 行")
        return analysis

    def detect_duplicates(self, key_columns: List[str] = None) -> Dict[str, any]:
        """
        检测重复记录
        使用患者姓名+管床医生作为复合键检测重复

        Args:
            key_columns: 用于判断重复的关键列，默认使用患者姓名+医生组合

        Returns:
            重复检测结果
        """
        if self.original_data is None:
            return {"error": "未加载数据"}

        try:
            # 自动检测关键列
            if key_columns is None:
                key_columns = self._detect_key_columns()

            logger.info(f"使用关键列检测重复: {key_columns}")

            # 检测重复
            duplicates = self.original_data.duplicated(subset=key_columns, keep=False)
            duplicate_records = self.original_data[duplicates]

            # 分组重复记录
            duplicate_groups = []
            patient_doctor_analysis = {}
            
            if len(duplicate_records) > 0:
                for key_values, group in duplicate_records.groupby(key_columns):
                    key_dict = dict(zip(key_columns, key_values)) if isinstance(key_values, tuple) else {key_columns[0]: key_values}
                    
                    duplicate_groups.append({
                        "key_values": key_dict,
                        "records": group.to_dict('records'),
                        "count": len(group)
                    })
                    
                    # 分析患者-医生组合
                    patient_name = key_dict.get('患者姓名', key_dict.get(key_columns[0], 'Unknown'))
                    doctor_name = key_dict.get('住院医生', key_dict.get('医生', 'Unknown'))
                    
                    if patient_name not in patient_doctor_analysis:
                        patient_doctor_analysis[patient_name] = {}
                    patient_doctor_analysis[patient_name][doctor_name] = len(group)

            self.duplicate_info = {
                "key_columns": key_columns,
                "total_duplicates": len(duplicate_records),
                "duplicate_groups": duplicate_groups,
                "unique_records": len(self.original_data) - len(duplicate_records),
                "patient_doctor_analysis": patient_doctor_analysis
            }

            logger.info(f"重复检测完成: 发现 {len(duplicate_records)} 条重复记录，{len(duplicate_groups)} 个重复组")
            self._log_processing("重复检测", f"基于患者+医生组合发现 {len(duplicate_records)} 条重复记录")

            return self.duplicate_info

        except Exception as e:
            logger.error(f"重复检测失败: {str(e)}")
            return {"error": str(e)}

    def _detect_key_columns(self) -> List[str]:
        """
        自动检测关键列（用于判断重复的列）
        现在使用患者姓名+管床医生作为复合键

        Returns:
            关键列名列表
        """
        columns = self.original_data.columns.tolist()
        key_columns = []

        # 常见的患者标识列名
        patient_name_patterns = ['患者姓名', '姓名', 'name', '病人姓名', '患者']
        doctor_name_patterns = ['医生', '住院医生', '主治医生', 'doctor', '医师', '管床医生']

        # 查找患者姓名列
        patient_col = None
        for col in columns:
            for pattern in patient_name_patterns:
                if pattern in col.lower() or col.lower() in pattern:
                    patient_col = col
                    key_columns.append(col)
                    break
            if patient_col:
                break

        # 查找医生姓名列
        doctor_col = None
        for col in columns:
            for pattern in doctor_name_patterns:
                if pattern in col.lower() or col.lower() in pattern:
                    doctor_col = col
                    key_columns.append(col)
                    break
            if doctor_col:
                break

        # 如果没找到患者姓名列，使用第一列
        if not patient_col and columns:
            key_columns.append(columns[0])
            logger.warning(f"未找到患者姓名列，使用第一列: {columns[0]}")

        # 如果没找到医生列，记录警告但继续处理
        if not doctor_col:
            logger.warning("未找到医生列，将只基于患者姓名进行去重")

        logger.info(f"检测到的关键列: {key_columns}")
        return key_columns

    def remove_duplicates(self, strategy: str = "keep_last") -> bool:
        """
        去除重复记录

        Args:
            strategy: 去重策略 ('keep_first', 'keep_last', 'remove_all')

        Returns:
            是否去重成功
        """
        if self.original_data is None:
            logger.error("未加载数据，无法执行去重")
            return False

        try:
            key_columns = self.duplicate_info.get("key_columns", self._detect_key_columns())

            logger.info(f"开始去重，策略: {strategy}")

            if strategy == "keep_first":
                self.cleaned_data = self.original_data.drop_duplicates(subset=key_columns, keep='first')
            elif strategy == "keep_last":
                self.cleaned_data = self.original_data.drop_duplicates(subset=key_columns, keep='last')
            elif strategy == "remove_all":
                # 移除所有重复记录（包括第一次出现的）
                duplicates = self.original_data.duplicated(subset=key_columns, keep=False)
                self.cleaned_data = self.original_data[~duplicates]
            else:
                raise ValueError(f"不支持的去重策略: {strategy}")

            removed_count = len(self.original_data) - len(self.cleaned_data)
            logger.info(f"去重完成，移除 {removed_count} 条记录，剩余 {len(self.cleaned_data)} 条记录")
            self._log_processing("数据去重", f"策略: {strategy}, 移除 {removed_count} 条记录")

            return True

        except Exception as e:
            logger.error(f"去重失败: {str(e)}")
            self._log_processing("数据去重", f"失败: {str(e)}")
            return False

    def get_cleaned_data(self) -> Optional[pd.DataFrame]:
        """
        获取清理后的数据

        Returns:
            清理后的DataFrame
        """
        return self.cleaned_data

    def save_cleaned_data(self, output_path: str) -> bool:
        """
        保存清理后的数据

        Args:
            output_path: 输出文件路径

        Returns:
            是否保存成功
        """
        if self.cleaned_data is None:
            logger.error("没有清理后的数据可保存")
            return False

        success = safe_write_excel(self.cleaned_data, output_path, "清理后的患者数据")
        if success:
            self._log_processing("数据保存", f"成功保存到: {output_path}")
        else:
            self._log_processing("数据保存", f"保存失败: {output_path}")

        return success

    def get_processing_summary(self) -> Dict[str, any]:
        """
        获取处理摘要

        Returns:
            处理摘要信息
        """
        summary = {
            "original_count": len(self.original_data) if self.original_data is not None else 0,
            "cleaned_count": len(self.cleaned_data) if self.cleaned_data is not None else 0,
            "removed_count": 0,
            "duplicate_groups": len(self.duplicate_info.get("duplicate_groups", [])),
            "key_columns_used": self.duplicate_info.get("key_columns", []),
            "patient_doctor_analysis": self.duplicate_info.get("patient_doctor_analysis", {}),
            "processing_log": self.processing_log.copy()
        }

        if summary["original_count"] > 0 and summary["cleaned_count"] > 0:
            summary["removed_count"] = summary["original_count"] - summary["cleaned_count"]

        return summary

    def _log_processing(self, step: str, message: str) -> None:
        """
        记录处理步骤

        Args:
            step: 处理步骤名称
            message: 处理消息
        """
        from datetime import datetime
        log_entry = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "step": step,
            "message": message
        }
        self.processing_log.append(log_entry)
        logger.info(f"[{step}] {message}")

    def validate_data_columns(self) -> Dict[str, any]:
        """
        验证数据列是否符合要求

        Returns:
            验证结果
        """
        if self.original_data is None:
            return {"valid": False, "error": "未加载数据"}

        required_columns = ["患者姓名", "住院医生"]  # 基于原VBA代码的必需列
        optional_columns = ["科室", "床号", "住院号"]

        existing_columns = self.original_data.columns.tolist()
        missing_required = []
        missing_optional = []

        # 检查必需列
        for col in required_columns:
            if col not in existing_columns:
                # 尝试模糊匹配
                found = False
                for existing_col in existing_columns:
                    if col in existing_col or existing_col in col:
                        found = True
                        break
                if not found:
                    missing_required.append(col)

        # 检查可选列
        for col in optional_columns:
            if col not in existing_columns:
                missing_optional.append(col)

        validation_result = {
            "valid": len(missing_required) == 0,
            "existing_columns": existing_columns,
            "required_columns": required_columns,
            "missing_required": missing_required,
            "missing_optional": missing_optional,
            "column_mapping": self._suggest_column_mapping(existing_columns, required_columns)
        }

        if validation_result["valid"]:
            logger.info("数据列验证通过")
        else:
            logger.warning(f"数据列验证失败，缺少必需列: {missing_required}")

        return validation_result

    def _suggest_column_mapping(self, existing_columns: List[str], required_columns: List[str]) -> Dict[str, str]:
        """
        建议列名映射

        Args:
            existing_columns: 现有列名
            required_columns: 必需列名

        Returns:
            列名映射建议
        """
        mapping = {}

        for required_col in required_columns:
            best_match = None
            max_similarity = 0

            for existing_col in existing_columns:
                # 简单的相似度计算
                similarity = 0
                if required_col in existing_col or existing_col in required_col:
                    similarity = min(len(required_col), len(existing_col)) / max(len(required_col), len(existing_col))

                if similarity > max_similarity:
                    max_similarity = similarity
                    best_match = existing_col

            if best_match and max_similarity > 0.5:
                mapping[required_col] = best_match

        return mapping
