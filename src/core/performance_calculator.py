#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绩效计算模块
负责处理绩效数据的计算和汇总
"""

import pandas as pd
import os
from typing import Dict, List, Tuple, Optional
import logging

from ..utils.logger import get_logger
from ..utils.file_utils import safe_read_csv, safe_read_excel
from .doctor_manager import Doctor<PERSON>ana<PERSON>

logger = get_logger()

class PerformanceCalculator:
    """绩效计算器"""
    
    def __init__(self, doctor_manager: Doctor<PERSON>ana<PERSON>):
        """
        初始化绩效计算器
        
        Args:
            doctor_manager: 医生管理器实例
        """
        self.doctor_manager = doctor_manager
        self.patient_data = None
        self.performance_data = {}  # 数据源名称 -> DataFrame
        self.calculated_results = None
        self.processing_log = []
        
        # 数据源配置（基于原VBA代码）
        self.data_sources = {
            "1.csv": "一病区",
            "2.csv": "二病区", 
            "3.csv": "三病区",
            "icu.csv": "呼吸ICU"
        }
    
    def load_patient_data(self, patient_data: pd.DataFrame) -> bool:
        """
        加载患者数据
        
        Args:
            patient_data: 患者数据DataFrame
        
        Returns:
            是否加载成功
        """
        try:
            self.patient_data = patient_data.copy()
            logger.info(f"患者数据加载成功，记录数: {len(self.patient_data)}")
            self._log_processing("患者数据加载", f"成功加载 {len(self.patient_data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"加载患者数据失败: {str(e)}")
            self._log_processing("患者数据加载", f"失败: {str(e)}")
            return False
    
    def load_performance_data(self, data_folder: str) -> Dict[str, bool]:
        """
        加载绩效数据
        
        Args:
            data_folder: 数据文件夹路径
        
        Returns:
            各数据源的加载结果
        """
        results = {}
        
        for filename, description in self.data_sources.items():
            file_path = os.path.join(data_folder, filename)
            
            try:
                if os.path.exists(file_path):
                    df = safe_read_csv(file_path)
                    if df is not None:
                        # 验证数据格式
                        if self._validate_performance_data(df, filename):
                            self.performance_data[filename] = df
                            results[filename] = True
                            logger.info(f"成功加载 {description} 数据: {len(df)} 条记录")
                            self._log_processing(f"数据加载-{description}", f"成功加载 {len(df)} 条记录")
                        else:
                            results[filename] = False
                            logger.error(f"{description} 数据格式验证失败")
                            self._log_processing(f"数据加载-{description}", "数据格式验证失败")
                    else:
                        results[filename] = False
                        logger.error(f"读取 {description} 数据失败")
                        self._log_processing(f"数据加载-{description}", "读取失败")
                else:
                    results[filename] = False
                    logger.warning(f"{description} 文件不存在: {file_path}")
                    self._log_processing(f"数据加载-{description}", "文件不存在")
                    
            except Exception as e:
                results[filename] = False
                logger.error(f"加载 {description} 数据异常: {str(e)}")
                self._log_processing(f"数据加载-{description}", f"异常: {str(e)}")
        
        successful_loads = sum(1 for success in results.values() if success)
        logger.info(f"数据加载完成，成功加载 {successful_loads}/{len(self.data_sources)} 个数据源")
        
        return results
    
    def _validate_performance_data(self, df: pd.DataFrame, filename: str) -> bool:
        """
        验证绩效数据格式
        
        Args:
            df: 数据DataFrame
            filename: 文件名
        
        Returns:
            是否验证通过
        """
        # 基于原VBA代码的必需字段
        required_columns = ["xm", "xz", "zx"]  # 姓名、协助点数、执行点数
        optional_columns = ["name", "doctor", "ys", "医生", "住院医生"]  # 项目名称、医生信息
        
        missing_columns = []
        for col in required_columns:
            if col not in df.columns:
                # 尝试模糊匹配
                found = False
                for existing_col in df.columns:
                    if col.lower() in existing_col.lower() or existing_col.lower() in col.lower():
                        found = True
                        break
                if not found:
                    missing_columns.append(col)
        
        if missing_columns:
            logger.error(f"{filename} 缺少必需列: {missing_columns}")
            return False
        
        # 检查是否包含医生信息（精确模式）
        has_doctor_info = any(col in df.columns for col in optional_columns[1:])  # 排除 'name'
        if has_doctor_info:
            logger.info(f"{filename} 检测到医生信息，将使用精确模式")
        else:
            logger.info(f"{filename} 未检测到医生信息，将使用兼容模式")
        
        # 检查数据类型
        try:
            # 确保协助点数和执行点数是数值类型
            if "xz" in df.columns:
                pd.to_numeric(df["xz"], errors='coerce')
            if "zx" in df.columns:
                pd.to_numeric(df["zx"], errors='coerce')
        except Exception as e:
            logger.error(f"{filename} 数据类型验证失败: {str(e)}")
            return False
        
        return True
    
    def calculate_performance(self) -> bool:
        """
        计算绩效数据
        
        Returns:
            是否计算成功
        """
        if self.patient_data is None:
            logger.error("未加载患者数据，无法计算绩效")
            return False
        
        if not self.performance_data:
            logger.error("未加载绩效数据，无法计算绩效")
            return False
        
        try:
            logger.info("开始计算绩效数据")
            
            # 初始化结果列表
            all_results = []
            
            # 处理每个数据源
            for filename, df in self.performance_data.items():
                source_name = self.data_sources[filename]
                logger.info(f"处理数据源: {source_name}")
                
                source_results = self._process_data_source(df, source_name)
                all_results.extend(source_results)
            
            # 转换为DataFrame
            if all_results:
                self.calculated_results = pd.DataFrame(all_results)
                logger.info(f"绩效计算完成，生成 {len(self.calculated_results)} 条结果记录")
                self._log_processing("绩效计算", f"成功生成 {len(self.calculated_results)} 条结果记录")
                return True
            else:
                logger.warning("没有生成任何绩效结果")
                self._log_processing("绩效计算", "没有生成任何结果")
                return False
                
        except Exception as e:
            logger.error(f"绩效计算失败: {str(e)}")
            self._log_processing("绩效计算", f"失败: {str(e)}")
            return False
    
    def _process_data_source(self, performance_df: pd.DataFrame, source_name: str) -> List[Dict]:
        """
        处理单个数据源
        支持精确模式（绩效数据包含医生）和兼容模式（仅患者姓名）
        
        Args:
            performance_df: 绩效数据DataFrame
            source_name: 数据源名称
        
        Returns:
            处理结果列表
        """
        results = []
        
        # 检查绩效数据是否包含医生信息
        doctor_columns = ["doctor", "ys", "医生", "住院医生", "管床医生"]
        perf_doctor_col = None
        for col in doctor_columns:
            if col in performance_df.columns:
                perf_doctor_col = col
                break
        
        use_precise_mode = perf_doctor_col is not None
        logger.info(f"{source_name} 使用{'精确模式' if use_precise_mode else '兼容模式'}")
        
        if use_precise_mode:
            # 精确模式：直接使用绩效数据中的医生信息
            results = self._process_precise_mode(performance_df, source_name, perf_doctor_col)
        else:
            # 兼容模式：通过患者数据关联医生信息
            results = self._process_compatible_mode(performance_df, source_name)
        
        logger.info(f"{source_name} 处理完成，生成 {len(results)} 条记录")
        return results
    
    def _process_precise_mode(self, performance_df: pd.DataFrame, source_name: str, doctor_col: str) -> List[Dict]:
        """
        精确模式处理：绩效数据包含医生信息
        
        Args:
            performance_df: 绩效数据DataFrame
            source_name: 数据源名称
            doctor_col: 医生列名
        
        Returns:
            处理结果列表
        """
        results = []
        
        for _, perf_row in performance_df.iterrows():
            try:
                patient_name = perf_row.get("xm", "")
                doctor_name = perf_row.get(doctor_col, "")
                
                if not patient_name or not doctor_name:
                    continue
                
                # 提取绩效数据
                assist_points = pd.to_numeric(perf_row.get("xz", 0), errors='coerce') or 0
                execute_points = pd.to_numeric(perf_row.get("zx", 0), errors='coerce') or 0
                total_points = assist_points + execute_points
                project_name = perf_row.get("name", "")
                
                # 获取医生分组
                doctor_group = self.doctor_manager.get_doctor_group(doctor_name)
                
                # 创建结果记录
                result = {
                    "患者姓名": patient_name,
                    "医生": doctor_name,
                    "协助点数": assist_points,
                    "执行点数": execute_points,
                    "综合点数": total_points,
                    "项目名称": project_name,
                    "医疗组": doctor_group,
                    "数据源": source_name,
                    "匹配模式": "精确模式"
                }
                
                results.append(result)
                
            except Exception as e:
                logger.warning(f"精确模式处理记录时出错 - 患者: {perf_row.get('xm', 'Unknown')}, 医生: {perf_row.get(doctor_col, 'Unknown')}, 错误: {str(e)}")
        
        return results
    
    def _process_compatible_mode(self, performance_df: pd.DataFrame, source_name: str) -> List[Dict]:
        """
        兼容模式处理：通过患者数据关联医生信息
        
        Args:
            performance_df: 绩效数据DataFrame
            source_name: 数据源名称
        
        Returns:
            处理结果列表
        """
        results = []
        unmatched_patients = set()
        
        # 遍历患者数据
        for _, patient_row in self.patient_data.iterrows():
            patient_name = patient_row.get("患者姓名", "")
            doctor_name = patient_row.get("住院医生", "")
            
            if not patient_name or not doctor_name:
                continue
            
            # 在绩效数据中查找匹配的记录
            matching_records = performance_df[performance_df["xm"] == patient_name]
            
            if len(matching_records) == 0:
                unmatched_patients.add(patient_name)
                continue
            
            for _, perf_row in matching_records.iterrows():
                try:
                    # 提取绩效数据
                    assist_points = pd.to_numeric(perf_row.get("xz", 0), errors='coerce') or 0
                    execute_points = pd.to_numeric(perf_row.get("zx", 0), errors='coerce') or 0
                    total_points = assist_points + execute_points
                    project_name = perf_row.get("name", "")
                    
                    # 使用患者数据中的医生信息
                    doctor_group = self.doctor_manager.get_doctor_group(doctor_name)
                    
                    # 创建结果记录
                    result = {
                        "患者姓名": patient_name,
                        "医生": doctor_name,
                        "协助点数": assist_points,
                        "执行点数": execute_points,
                        "综合点数": total_points,
                        "项目名称": project_name,
                        "医疗组": doctor_group,
                        "数据源": source_name,
                        "匹配模式": "兼容模式"
                    }
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"兼容模式处理记录时出错 - 患者: {patient_name}, 医生: {doctor_name}, 错误: {str(e)}")
        
        # 记录未匹配的患者
        if unmatched_patients:
            logger.warning(f"{source_name} 中有 {len(unmatched_patients)} 个患者未在绩效数据中找到匹配记录")
            self._log_processing(f"{source_name}-未匹配患者", f"{len(unmatched_patients)} 个患者: {list(unmatched_patients)[:10]}...") if len(unmatched_patients) > 10 else self._log_processing(f"{source_name}-未匹配患者", f"{len(unmatched_patients)} 个患者: {list(unmatched_patients)}")
        
        return results
    
    def get_individual_summary(self) -> Optional[pd.DataFrame]:
        """
        获取医生个人绩效汇总
        
        Returns:
            医生个人绩效汇总DataFrame
        """
        if self.calculated_results is None:
            return None
        
        try:
            # 按医生分组汇总
            summary = self.calculated_results.groupby("医生").agg({
                "协助点数": "sum",
                "执行点数": "sum", 
                "综合点数": "sum",
                "患者姓名": ["nunique", "count"],  # 唯一患者数量和数据记录数量
                "医疗组": "first"  # 医疗组
            }).reset_index()
            
            # 扁平化列名
            summary.columns = ['医生', '协助点数', '执行点数', '综合点数', '患者数量', '数据记录数', '医疗组']
            
            # 排序
            summary = summary.sort_values("综合点数", ascending=False)
            
            logger.info(f"生成医生个人绩效汇总，共 {len(summary)} 名医生")
            return summary
            
        except Exception as e:
            logger.error(f"生成医生个人绩效汇总失败: {str(e)}")
            return None
    
    def get_group_summary(self) -> Optional[pd.DataFrame]:
        """
        获取医疗组绩效汇总
        
        Returns:
            医疗组绩效汇总DataFrame
        """
        if self.calculated_results is None:
            return None
        
        try:
            # 按医疗组分组汇总
            group_summary = self.calculated_results.groupby("医疗组").agg({
                "协助点数": "sum",
                "执行点数": "sum",
                "综合点数": "sum",
                "患者姓名": ["nunique", "count"],  # 唯一患者数量和数据记录数量
                "医生": "nunique"  # 医生数量
            }).reset_index()
            
            # 扁平化列名
            group_summary.columns = ['医疗组', '协助点数', '执行点数', '综合点数', '患者数量', '数据记录数', '医生数量']
            
            # 排序
            group_summary = group_summary.sort_values("综合点数", ascending=False)
            
            logger.info(f"生成医疗组绩效汇总，共 {len(group_summary)} 个医疗组")
            return group_summary
            
        except Exception as e:
            logger.error(f"生成医疗组绩效汇总失败: {str(e)}")
            return None
    
    def get_detailed_results(self) -> Optional[pd.DataFrame]:
        """
        获取详细绩效结果
        
        Returns:
            详细绩效结果DataFrame
        """
        return self.calculated_results
    
    def get_processing_log(self) -> List[Dict]:
        """
        获取处理日志
        
        Returns:
            处理日志列表
        """
        return self.processing_log.copy()
    
    def _log_processing(self, step: str, message: str) -> None:
        """
        记录处理步骤
        
        Args:
            step: 处理步骤名称
            message: 处理消息
        """
        from datetime import datetime
        log_entry = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "step": step,
            "message": message
        }
        self.processing_log.append(log_entry)
        logger.info(f"[{step}] {message}")
