#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成模块
负责生成Excel格式的绩效报告
"""

import pandas as pd
import os
from typing import Dict, List, Optional
import logging
from datetime import datetime

from ..utils.logger import get_logger
from ..utils.file_utils import create_output_filename

logger = get_logger()

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.output_path = None
        self.generation_log = []
    
    def generate_excel_report(
        self,
        detailed_results: pd.DataFrame,
        individual_summary: pd.DataFrame,
        group_summary: pd.DataFrame,
        processing_log: List[Dict],
        doctor_manager=None,
        output_dir: str = "output"
    ) -> Optional[str]:
        """
        生成Excel格式的绩效报告
        
        Args:
            detailed_results: 详细绩效结果
            individual_summary: 医生个人绩效汇总
            group_summary: 医疗组绩效汇总
            processing_log: 处理日志
            doctor_manager: 医生管理器（用于导出分组信息）
            output_dir: 输出目录
        
        Returns:
            生成的文件路径，失败时返回None
        """
        try:
            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成文件名
            filename = create_output_filename("绩效统计报告", ".xlsx")
            self.output_path = os.path.join(output_dir, filename)
            
            logger.info(f"开始生成Excel报告: {self.output_path}")
            
            # 使用ExcelWriter创建多工作表Excel文件
            with pd.ExcelWriter(self.output_path, engine='openpyxl') as writer:
                # 写入各个工作表
                self._write_individual_summary(individual_summary, writer)
                self._write_group_summary(group_summary, writer)
                self._write_detailed_results(detailed_results, writer)
                self._write_doctor_groups(doctor_manager, writer)
                self._write_processing_log(processing_log, writer)
                self._write_summary_sheet(individual_summary, group_summary, detailed_results, writer)
            
            # 美化Excel文件
            self._format_excel_file(self.output_path)
            
            logger.info(f"Excel报告生成成功: {self.output_path}")
            self._log_generation("报告生成", f"成功生成: {self.output_path}")
            
            return self.output_path
            
        except Exception as e:
            logger.error(f"生成Excel报告失败: {str(e)}")
            self._log_generation("报告生成", f"失败: {str(e)}")
            return None
    
    def _write_individual_summary(self, df: pd.DataFrame, writer: pd.ExcelWriter) -> None:
        """写入医生个人绩效汇总工作表"""
        if df is not None and not df.empty:
            df.to_excel(writer, sheet_name='医生个人绩效', index=False)
            logger.info(f"写入医生个人绩效数据: {len(df)} 行")
        else:
            # 创建空工作表
            pd.DataFrame({"说明": ["暂无数据"]}).to_excel(writer, sheet_name='医生个人绩效', index=False)
    
    def _write_group_summary(self, df: pd.DataFrame, writer: pd.ExcelWriter) -> None:
        """写入医疗组绩效汇总工作表"""
        if df is not None and not df.empty:
            df.to_excel(writer, sheet_name='医疗组汇总', index=False)
            logger.info(f"写入医疗组汇总数据: {len(df)} 行")
        else:
            pd.DataFrame({"说明": ["暂无数据"]}).to_excel(writer, sheet_name='医疗组汇总', index=False)
    
    def _write_detailed_results(self, df: pd.DataFrame, writer: pd.ExcelWriter) -> None:
        """写入详细绩效结果工作表"""
        if df is not None and not df.empty:
            df.to_excel(writer, sheet_name='详细绩效数据', index=False)
            logger.info(f"写入详细绩效数据: {len(df)} 行")
        else:
            pd.DataFrame({"说明": ["暂无数据"]}).to_excel(writer, sheet_name='详细绩效数据', index=False)
    
    def _write_doctor_groups(self, doctor_manager, writer: pd.ExcelWriter) -> None:
        """写入医生分组信息工作表"""
        try:
            if doctor_manager is not None:
                # 获取医生分组信息
                all_doctors = doctor_manager.get_all_doctors()
                all_groups = doctor_manager.get_all_groups()
                
                # 构建完整的分组医生字典
                group_doctors = {}
                for group_name in all_groups:
                    group_doctors[group_name] = doctor_manager.get_group_doctors(group_name)
                
                # 创建医生分组明细表
                doctor_group_data = []
                for doctor in sorted(all_doctors):
                    group = doctor_manager.get_doctor_group(doctor)
                    doctor_group_data.append({
                        "医生姓名": doctor,
                        "所属医疗组": group
                    })
                
                doctor_group_df = pd.DataFrame(doctor_group_data)
                doctor_group_df.to_excel(writer, sheet_name='医生分组明细', index=False)
                
                # 创建分组汇总表
                group_summary_data = []
                for group_name, doctors in group_doctors.items():
                    group_summary_data.append({
                        "医疗组": group_name,
                        "医生数量": len(doctors),
                        "医生名单": "、".join(sorted(doctors))
                    })
                
                group_summary_df = pd.DataFrame(group_summary_data)
                # 按医生数量降序排列
                group_summary_df = group_summary_df.sort_values("医生数量", ascending=False)
                group_summary_df.to_excel(writer, sheet_name='医疗组结构', index=False)
                
                # 创建统计信息表
                stats_data = [
                    ["总医生数量", len(all_doctors)],
                    ["医疗组数量", len(group_doctors)],
                    ["平均每组医生数", round(len(all_doctors) / len(group_doctors), 1) if group_doctors else 0],
                    ["", ""],
                    ["医疗组详情", ""]
                ]
                
                # 添加各组详细信息
                for group_name, doctors in sorted(group_doctors.items()):
                    stats_data.append([f"{group_name}", f"{len(doctors)}人"])
                    
                stats_data.append(["", ""])
                stats_data.append(["更新时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
                
                stats_df = pd.DataFrame(stats_data, columns=["项目", "信息"])
                stats_df.to_excel(writer, sheet_name='分组统计', index=False)
                
                logger.info(f"写入医生分组信息: {len(all_doctors)} 名医生，{len(group_doctors)} 个医疗组")
                
            else:
                # 没有医生管理器时创建空工作表
                pd.DataFrame({"说明": ["医生分组信息不可用"]}).to_excel(writer, sheet_name='医生分组明细', index=False)
                pd.DataFrame({"说明": ["医疗组信息不可用"]}).to_excel(writer, sheet_name='医疗组结构', index=False)
                pd.DataFrame({"说明": ["分组统计不可用"]}).to_excel(writer, sheet_name='分组统计', index=False)
                
        except Exception as e:
            logger.error(f"写入医生分组信息失败: {str(e)}")
            # 创建错误说明工作表
            error_df = pd.DataFrame({"说明": [f"医生分组信息导出失败: {str(e)}"]})
            error_df.to_excel(writer, sheet_name='医生分组明细', index=False)
    
    def _write_processing_log(self, log_data: List[Dict], writer: pd.ExcelWriter) -> None:
        """写入数据处理日志工作表"""
        if log_data:
            log_df = pd.DataFrame(log_data)
            log_df.to_excel(writer, sheet_name='数据处理日志', index=False)
            logger.info(f"写入处理日志: {len(log_data)} 条")
        else:
            pd.DataFrame({"说明": ["暂无日志"]}).to_excel(writer, sheet_name='数据处理日志', index=False)
    
    def _write_summary_sheet(
        self,
        individual_summary: pd.DataFrame,
        group_summary: pd.DataFrame,
        detailed_results: pd.DataFrame,
        writer: pd.ExcelWriter
    ) -> None:
        """写入汇总信息工作表"""
        summary_data = []
        
        # 基本统计信息
        summary_data.append(["报告生成时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
        summary_data.append(["", ""])
        
        # 数据统计
        if detailed_results is not None and not detailed_results.empty:
            summary_data.append(["详细记录总数", len(detailed_results)])
            summary_data.append(["总协助点数", detailed_results["协助点数"].sum()])
            summary_data.append(["总执行点数", detailed_results["执行点数"].sum()])
            summary_data.append(["总综合点数", detailed_results["综合点数"].sum()])
        else:
            summary_data.append(["详细记录总数", 0])
            summary_data.append(["总协助点数", 0])
            summary_data.append(["总执行点数", 0])
            summary_data.append(["总综合点数", 0])
        
        summary_data.append(["", ""])
        
        if individual_summary is not None and not individual_summary.empty:
            summary_data.append(["参与医生数量", len(individual_summary)])
        else:
            summary_data.append(["参与医生数量", 0])
        
        if group_summary is not None and not group_summary.empty:
            summary_data.append(["医疗组数量", len(group_summary)])
        else:
            summary_data.append(["医疗组数量", 0])
        
        summary_data.append(["", ""])
        summary_data.append(["说明", "本报告基于医院科室绩效计算器生成"])
        
        # 创建汇总DataFrame
        summary_df = pd.DataFrame(summary_data, columns=["项目", "数值"])
        summary_df.to_excel(writer, sheet_name='报告汇总', index=False)
        
        logger.info("写入报告汇总信息")
    
    def _format_excel_file(self, file_path: str) -> None:
        """
        美化Excel文件格式
        
        Args:
            file_path: Excel文件路径
        """
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            
            # 加载工作簿
            wb = load_workbook(file_path)
            
            # 定义样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style="thin"),
                right=Side(style="thin"),
                top=Side(style="thin"),
                bottom=Side(style="thin")
            )
            
            # 格式化每个工作表
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                # 设置表头样式
                if ws.max_row > 0:
                    for cell in ws[1]:  # 第一行
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = center_alignment
                        cell.border = border
                
                # 设置数据行边框
                for row in ws.iter_rows(min_row=2, max_row=ws.max_row, max_col=ws.max_column):
                    for cell in row:
                        cell.border = border
                        if isinstance(cell.value, (int, float)):
                            cell.alignment = Alignment(horizontal="right", vertical="center")
                        else:
                            cell.alignment = Alignment(horizontal="left", vertical="center")
                
                # 自动调整列宽
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存格式化后的文件
            wb.save(file_path)
            logger.info("Excel文件格式化完成")
            
        except Exception as e:
            logger.warning(f"Excel文件格式化失败: {str(e)}")
    
    def get_generation_log(self) -> List[Dict]:
        """
        获取生成日志
        
        Returns:
            生成日志列表
        """
        return self.generation_log.copy()
    
    def _log_generation(self, step: str, message: str) -> None:
        """
        记录生成步骤
        
        Args:
            step: 生成步骤名称
            message: 生成消息
        """
        log_entry = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "step": step,
            "message": message
        }
        self.generation_log.append(log_entry)
        logger.info(f"[{step}] {message}")
    
    def create_sample_data_template(self, output_dir: str = "templates") -> Optional[str]:
        """
        创建示例数据模板
        
        Args:
            output_dir: 输出目录
        
        Returns:
            模板文件路径
        """
        try:
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 创建患者数据模板
            patients_template = pd.DataFrame({
                "患者姓名": ["张三", "李四", "王五", "赵六"],
                "住院医生": ["赖红琳", "吴西雅", "童波", "夏顺生"],
                "科室": ["内科", "外科", "儿科", "妇科"],
                "床号": ["101", "102", "103", "104"],
                "住院号": ["2024001", "2024002", "2024003", "2024004"]
            })
            
            patients_file = os.path.join(output_dir, "patients_template.xlsx")
            patients_template.to_excel(patients_file, index=False)
            
            # 创建绩效数据模板
            performance_template = pd.DataFrame({
                "xm": ["张三", "李四", "王五", "赵六"],
                "xz": [10, 15, 8, 12],  # 协助点数
                "zx": [20, 25, 18, 22],  # 执行点数
                "name": ["手术协助", "护理服务", "检查协助", "治疗服务"]
            })
            
            for csv_name in ["1.csv", "2.csv", "3.csv", "icu.csv"]:
                csv_file = os.path.join(output_dir, csv_name)
                performance_template.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"示例数据模板创建成功: {output_dir}")
            return output_dir
            
        except Exception as e:
            logger.error(f"创建示例数据模板失败: {str(e)}")
            return None
