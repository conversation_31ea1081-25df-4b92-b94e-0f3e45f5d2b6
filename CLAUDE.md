# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Hospital Department Performance Calculator** (医院科室绩效计算器) - a modernized Python application that processes hospital performance data, calculates metrics for doctors and medical teams, and generates comprehensive Excel reports. The project has both desktop GUI and web-based interfaces.

## Core Architecture

### Dual Interface System
- **Desktop Application**: `python main.py` - tkinter-based GUI 
- **Web Application**: `python app.py` - Flask-based web interface

### Key Business Logic (`src/core/`)
- **Patient Processing**: Intelligent deduplication using patient name + doctor composite keys
- **Performance Calculation**: Multi-source data aggregation from 4 ward data sources
- **Doctor Management**: Configurable medical team groupings with JSON persistence
- **Report Generation**: 8-worksheet Excel reports with comprehensive statistics

### Data Processing Pipeline
1. **Input Validation**: Requires `patients.xlsx` + 4 CSV files (`1.csv`, `2.csv`, `3.csv`, `icu.csv`)
2. **Smart Deduplication**: Uses patient name + attending doctor to avoid false duplicates
3. **Dual Processing Modes**:
   - *Precision Mode*: When performance data contains doctor info
   - *Compatibility Mode*: Legacy format support via patient data linking
4. **Medical Team Assignment**: Maps performance to doctor groups
5. **Multi-worksheet Reporting**: Individual, group, and administrative statistics

## Common Development Commands

### Desktop Application
```bash
# Install desktop dependencies
pip install -r requirements.txt

# Run desktop GUI
python main.py
```

### Web Application Development
```bash
# Quick local test setup (recommended)
./start_local_test.sh          # macOS/Linux
start_local_test.bat           # Windows

# Manual setup
python3 -m venv web_venv
source web_venv/bin/activate   # macOS/Linux
# web_venv\Scripts\activate    # Windows
pip install -r web_requirements.txt

# Initialize database
python -c "from app import app, db; app.app_context().push(); db.create_all()"

# Run web application
python app.py                  # Runs on http://localhost:5000
```

### Production Deployment
```bash
# Production web server
gunicorn --bind 127.0.0.1:5000 --workers 4 --timeout 300 app:app

# Background task worker  
celery -A app.celery worker --loglevel=info
```

### Testing
```bash
# Create test data (done automatically by start_local_test scripts)
# Test data includes: patients.xlsx, 1.csv, 2.csv, 3.csv, icu.csv

# Web application testing
# 1. Run ./start_local_test.sh
# 2. Open http://localhost:5000
# 3. Register user or login
# 4. Upload test files from test_data/ directory
# 5. Execute performance calculation
```

## Key Configuration Files

- **`config.py`**: Desktop application configuration, doctor groups, data validation rules
- **`web_config.py`**: Flask app configuration for different environments
- **`医生分组配置.json`**: Persistent doctor group assignments (auto-generated)
- **`.env`**: Web application environment variables (copy from `.env.example`)

## Required Data Format

### Input Files Structure
```
data-folder/
├── patients.xlsx          # Patient roster with doctor assignments
├── 1.csv                 # Ward 1 performance data  
├── 2.csv                 # Ward 2 performance data
├── 3.csv                 # Ward 3 performance data
└── icu.csv               # ICU performance data
```

### patients.xlsx Required Columns
- `患者姓名` (Patient Name) - Required
- `住院医生` (Attending Doctor) - Required  
- `科室`, `床号`, `住院号` - Optional

### Performance CSV Required Columns
- `xm` (Patient Name) - Required, matches patients.xlsx
- `xz` (Cooperation Points) - Required, numeric
- `zx` (Execution Points) - Required, numeric
- `doctor`/`ys`/`医生` - Optional, enables Precision Mode

## Development Patterns

### Error Handling
- All core modules use comprehensive logging via `src/utils/logger.py`
- GUI components show user-friendly error messages with detailed logs
- Web application uses Flask error handlers with proper HTTP status codes

### Data Validation
- File structure validation before processing
- Column name mapping suggestions for incorrect formats
- Duplicate detection reports before removal
- Processing mode auto-detection (Precision vs Compatibility)

### Configuration Management
- Desktop: `config.py` with nested configuration sections
- Web: Environment-based configuration classes in `web_config.py`
- Doctor groups: JSON persistence with automatic backup on changes

### Medical Team Logic
Default doctor groups (configurable):
- 赖红琳组, 吴西雅组, 童波组, 夏顺生组, 邹国明组, 其他组
- Groups are editable through GUI dialog or web interface
- Performance is aggregated by individual doctor and medical team

## Important Notes

- **Version 1.1.0** introduced smart deduplication using composite keys (patient + doctor)
- The system handles same-name patients with different doctors correctly
- Two processing modes auto-detected based on data format
- Excel reports include 8 worksheets: individual performance, team summaries, detailed data, grouping info, processing logs, and metadata
- Web version supports file upload limits up to 50MB
- Production deployment supports Docker containerization and cloud services

## Deployment Environments

- **Local Development**: SQLite database, Flask dev server
- **Production**: MySQL database, Gunicorn + Nginx, Redis for task queuing
- **Cloud**: Full deployment guides available for Tencent Cloud
- **Docker**: Multi-container setup with docker-compose.yml