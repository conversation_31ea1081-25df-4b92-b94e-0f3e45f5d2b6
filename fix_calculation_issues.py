#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绩效计算问题诊断和修复工具
用于识别和解决绩效计算失败的根本原因
"""

import os
import sys
import pandas as pd
import json
from typing import Dict, List, Tuple, Optional

def check_file_structure(data_folder: str) -> Tuple[bool, Dict[str, any]]:
    """
    检查数据文件夹结构
    
    Args:
        data_folder: 数据文件夹路径
    
    Returns:
        (是否有效, 检查结果详情)
    """
    print(f"🔍 检查数据文件夹: {data_folder}")
    
    required_files = {
        "patients.xlsx": "患者数据文件",
        "1.csv": "一病区绩效数据",
        "2.csv": "二病区绩效数据", 
        "3.csv": "三病区绩效数据",
        "icu.csv": "呼吸ICU绩效数据"
    }
    
    results = {
        "folder_exists": os.path.exists(data_folder),
        "files": {},
        "missing_files": [],
        "found_files": [],
        "total_files": len(required_files)
    }
    
    if not results["folder_exists"]:
        print(f"❌ 数据文件夹不存在: {data_folder}")
        return False, results
    
    print(f"✅ 数据文件夹存在: {data_folder}")
    
    for filename, description in required_files.items():
        file_path = os.path.join(data_folder, filename)
        file_info = {
            "path": file_path,
            "exists": os.path.exists(file_path),
            "description": description
        }
        
        if file_info["exists"]:
            file_info["size"] = os.path.getsize(file_path)
            results["found_files"].append(filename)
            print(f"✅ {filename}: {file_info['size']} 字节 - {description}")
        else:
            results["missing_files"].append(filename)
            print(f"❌ {filename}: 文件不存在 - {description}")
        
        results["files"][filename] = file_info
    
    is_valid = len(results["missing_files"]) == 0
    print(f"\n📊 文件结构检查结果:")
    print(f"   找到文件: {len(results['found_files'])}/{results['total_files']}")
    print(f"   缺失文件: {len(results['missing_files'])}")
    
    return is_valid, results

def check_excel_file(file_path: str) -> Tuple[bool, Dict[str, any]]:
    """
    检查Excel文件格式和内容
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        (是否有效, 检查结果详情)
    """
    print(f"\n📋 检查Excel文件: {os.path.basename(file_path)}")
    
    results = {
        "readable": False,
        "columns": [],
        "row_count": 0,
        "required_columns": ["患者姓名", "住院医生"],
        "missing_columns": [],
        "sample_data": None
    }
    
    try:
        df = pd.read_excel(file_path)
        results["readable"] = True
        results["columns"] = list(df.columns)
        results["row_count"] = len(df)
        
        print(f"✅ 文件读取成功")
        print(f"   行数: {results['row_count']}")
        print(f"   列数: {len(results['columns'])}")
        print(f"   列名: {results['columns']}")
        
        # 检查必需列
        for col in results["required_columns"]:
            found = False
            for existing_col in results["columns"]:
                if col in existing_col or existing_col in col:
                    found = True
                    break
            if not found:
                results["missing_columns"].append(col)
        
        if results["missing_columns"]:
            print(f"⚠️ 缺少必需列: {results['missing_columns']}")
        else:
            print(f"✅ 包含所有必需列")
        
        # 显示示例数据
        if len(df) > 0:
            results["sample_data"] = df.head(3).to_dict('records')
            print(f"📄 示例数据 (前3行):")
            for i, row in enumerate(results["sample_data"]):
                print(f"   行{i+1}: {row}")
        
        return len(results["missing_columns"]) == 0, results
        
    except Exception as e:
        print(f"❌ 文件读取失败: {str(e)}")
        results["error"] = str(e)
        return False, results

def check_csv_file(file_path: str) -> Tuple[bool, Dict[str, any]]:
    """
    检查CSV文件格式和内容
    
    Args:
        file_path: CSV文件路径
    
    Returns:
        (是否有效, 检查结果详情)
    """
    print(f"\n📄 检查CSV文件: {os.path.basename(file_path)}")
    
    results = {
        "readable": False,
        "columns": [],
        "row_count": 0,
        "required_columns": ["xm", "xz", "zx"],  # 姓名、协助点数、执行点数
        "missing_columns": [],
        "sample_data": None,
        "encoding": None
    }
    
    # 尝试不同编码
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            results["readable"] = True
            results["encoding"] = encoding
            results["columns"] = list(df.columns)
            results["row_count"] = len(df)
            
            print(f"✅ 文件读取成功 (编码: {encoding})")
            print(f"   行数: {results['row_count']}")
            print(f"   列数: {len(results['columns'])}")
            print(f"   列名: {results['columns']}")
            
            # 检查必需列
            for col in results["required_columns"]:
                found = False
                for existing_col in results["columns"]:
                    if col.lower() in existing_col.lower() or existing_col.lower() in col.lower():
                        found = True
                        break
                if not found:
                    results["missing_columns"].append(col)
            
            if results["missing_columns"]:
                print(f"⚠️ 缺少必需列: {results['missing_columns']}")
                print(f"   提示: 需要包含姓名(xm)、协助点数(xz)、执行点数(zx)列")
            else:
                print(f"✅ 包含所有必需列")
            
            # 显示示例数据
            if len(df) > 0:
                results["sample_data"] = df.head(3).to_dict('records')
                print(f"📄 示例数据 (前3行):")
                for i, row in enumerate(results["sample_data"]):
                    print(f"   行{i+1}: {row}")
            
            return len(results["missing_columns"]) == 0, results
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"❌ 文件读取失败 (编码: {encoding}): {str(e)}")
            results["error"] = str(e)
            break
    
    if not results["readable"]:
        print(f"❌ 无法使用任何编码读取文件")
    
    return False, results

def diagnose_calculation_issues(data_folder: str) -> Dict[str, any]:
    """
    全面诊断绩效计算问题
    
    Args:
        data_folder: 数据文件夹路径
    
    Returns:
        诊断结果
    """
    print("🏥 绩效计算问题诊断工具")
    print("=" * 60)
    
    diagnosis = {
        "folder_check": None,
        "excel_check": None,
        "csv_checks": {},
        "overall_status": "unknown",
        "recommendations": []
    }
    
    # 1. 检查文件夹结构
    folder_valid, folder_results = check_file_structure(data_folder)
    diagnosis["folder_check"] = folder_results
    
    if not folder_valid:
        diagnosis["overall_status"] = "failed"
        diagnosis["recommendations"].append("请确保数据文件夹包含所有必需文件")
        return diagnosis
    
    # 2. 检查Excel文件
    patients_file = os.path.join(data_folder, "patients.xlsx")
    excel_valid, excel_results = check_excel_file(patients_file)
    diagnosis["excel_check"] = excel_results
    
    # 3. 检查CSV文件
    csv_files = ["1.csv", "2.csv", "3.csv", "icu.csv"]
    csv_all_valid = True
    
    for csv_file in csv_files:
        csv_path = os.path.join(data_folder, csv_file)
        if os.path.exists(csv_path):
            csv_valid, csv_results = check_csv_file(csv_path)
            diagnosis["csv_checks"][csv_file] = csv_results
            if not csv_valid:
                csv_all_valid = False
        else:
            diagnosis["csv_checks"][csv_file] = {"readable": False, "error": "文件不存在"}
            csv_all_valid = False
    
    # 4. 生成总体状态和建议
    if excel_valid and csv_all_valid:
        diagnosis["overall_status"] = "passed"
        diagnosis["recommendations"].append("✅ 所有文件检查通过，数据格式正确")
    else:
        diagnosis["overall_status"] = "failed"
        
        if not excel_valid:
            diagnosis["recommendations"].append("❌ 患者数据文件(patients.xlsx)存在问题，请检查文件格式和必需列")
        
        if not csv_all_valid:
            diagnosis["recommendations"].append("❌ 部分绩效数据文件(CSV)存在问题，请检查文件格式和必需列")
    
    return diagnosis

def generate_fix_recommendations(diagnosis: Dict[str, any]) -> List[str]:
    """
    根据诊断结果生成修复建议
    
    Args:
        diagnosis: 诊断结果
    
    Returns:
        修复建议列表
    """
    recommendations = []
    
    print("\n🔧 修复建议:")
    print("-" * 40)
    
    # 文件夹问题
    if diagnosis["folder_check"] and not diagnosis["folder_check"]["folder_exists"]:
        rec = "1. 创建数据文件夹并放置所有必需文件"
        recommendations.append(rec)
        print(rec)
    
    # 缺失文件问题
    if diagnosis["folder_check"] and diagnosis["folder_check"]["missing_files"]:
        missing = diagnosis["folder_check"]["missing_files"]
        rec = f"2. 添加缺失文件: {', '.join(missing)}"
        recommendations.append(rec)
        print(rec)
    
    # Excel文件问题
    if diagnosis["excel_check"] and not diagnosis["excel_check"]["readable"]:
        rec = "3. 修复患者数据文件(patients.xlsx)格式问题"
        recommendations.append(rec)
        print(rec)
    elif diagnosis["excel_check"] and diagnosis["excel_check"]["missing_columns"]:
        missing_cols = diagnosis["excel_check"]["missing_columns"]
        rec = f"4. 在患者数据文件中添加必需列: {', '.join(missing_cols)}"
        recommendations.append(rec)
        print(rec)
    
    # CSV文件问题
    for csv_file, results in diagnosis["csv_checks"].items():
        if not results.get("readable", False):
            rec = f"5. 修复{csv_file}文件格式或编码问题"
            recommendations.append(rec)
            print(rec)
        elif results.get("missing_columns"):
            missing_cols = results["missing_columns"]
            rec = f"6. 在{csv_file}中添加必需列: {', '.join(missing_cols)}"
            recommendations.append(rec)
            print(rec)
    
    # 通用建议
    if diagnosis["overall_status"] == "failed":
        general_recs = [
            "7. 确保Excel文件包含'患者姓名'和'住院医生'列",
            "8. 确保CSV文件包含'xm'(姓名)、'xz'(协助点数)、'zx'(执行点数)列",
            "9. 检查文件编码，建议使用UTF-8编码",
            "10. 确保数据文件中没有空行或格式错误"
        ]
        recommendations.extend(general_recs)
        for rec in general_recs:
            print(rec)
    
    return recommendations

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python fix_calculation_issues.py <数据文件夹路径>")
        print("示例: python fix_calculation_issues.py test_data")
        sys.exit(1)
    
    data_folder = sys.argv[1]
    
    # 执行诊断
    diagnosis = diagnose_calculation_issues(data_folder)
    
    # 生成修复建议
    recommendations = generate_fix_recommendations(diagnosis)
    
    # 保存诊断报告
    report_file = "calculation_diagnosis_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 诊断报告已保存到: {report_file}")
    
    # 显示总结
    print("\n📊 诊断总结:")
    print("-" * 40)
    print(f"总体状态: {'✅ 通过' if diagnosis['overall_status'] == 'passed' else '❌ 失败'}")
    print(f"建议数量: {len(recommendations)}")
    
    if diagnosis["overall_status"] == "passed":
        print("\n🎉 恭喜！数据文件格式正确，应该可以正常进行绩效计算。")
        print("如果仍然遇到计算失败，请检查:")
        print("- 应用程序是否正确启动")
        print("- 是否选择了正确的数据文件夹")
        print("- 查看日志文件获取详细错误信息")
    else:
        print("\n⚠️ 发现问题，请按照上述建议修复后重试。")

if __name__ == "__main__":
    main()
