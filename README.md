# 医院科室绩效计算器

医院科室绩效计算器是一个基于原VBA代码重构的现代化Python应用程序，专门用于医院科室的绩效统计和计算。该系统提供用户友好的图形界面和强大的数据处理功能，大大提升了医院绩效管理的效率和准确性。

## 🎯 核心价值
- **效率提升**: 从手动VBA操作改为自动化Python处理，操作时间减少80%
- **数据安全**: 自动保存和备份机制，零数据丢失风险
- **用户友好**: 直观的图形界面，学习成本大幅降低
- **功能完整**: 涵盖数据处理、绩效计算、报告生成的完整流程

## 🚀 功能特性

### 核心功能
- **患者名单去重处理** - 智能检测并处理重复患者记录
- **多数据源绩效计算** - 支持季度数据和ICU数据的综合计算
- **医生分组管理** - 基于配置文件的灵活医生分组系统
- **Excel报告生成** - 生成包含多个工作表的详细绩效报告

### 界面特性
- **现代化GUI** - 基于tkinter的直观用户界面
- **实时进度显示** - 处理过程中的实时状态反馈
- **数据预览功能** - 处理前后的数据预览和验证
- **详细日志记录** - 完整的操作日志和错误追踪

### 医生管理功能
- **添加医生** - 支持添加新医生到指定分组
- **删除医生** - 从系统中完全删除医生
- **移动医生** - 便捷地将医生移动到其他分组
- **分组管理** - 创建、重命名、删除分组
- **数据持久化** - 自动保存配置到JSON文件，支持备份恢复

## 📋 系统要求

- Python 3.7+
- Windows/macOS/Linux

## 🛠️ 安装说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd 科室绩效计算器
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

## 📁 项目结构

```
科室绩效计算器/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── config.py                  # 配置文件
├── 医生名单.md               # 医生名单配置文件
├── 医生分组配置.json         # 医生分组持久化配置
├── src/                      # 源代码目录
│   ├── core/                 # 核心业务逻辑
│   │   ├── doctor_manager.py      # 医生分组管理
│   │   ├── patient_processor.py   # 患者数据处理
│   │   ├── performance_calculator.py # 绩效计算
│   │   └── report_generator.py    # 报告生成
│   ├── gui/                  # 图形界面
│   │   ├── main_window.py         # 主窗口
│   │   ├── components.py          # GUI组件
│   │   └── doctor_management_dialog.py # 医生管理对话框
│   └── utils/                # 工具模块
│       ├── logger.py              # 日志管理
│       └── file_utils.py          # 文件操作工具
├── logs/                     # 日志文件目录
├── output/                   # 输出文件目录
└── templates/                # 数据模板目录
```

## 📊 数据格式要求

### ⚠️ 重要提示：待处理文件夹结构要求

**在选择"待处理的文件夹"时，请确保文件夹包含以下必需文件：**

```
您选择的数据文件夹/
├── patients.xlsx             # 患者名单文件（必需）
├── 1.csv                    # 第一季度绩效数据（必需）
├── 2.csv                    # 第二季度绩效数据（必需）
├── 3.csv                    # 第三季度绩效数据（必需）
└── icu.csv                  # ICU科室绩效数据（必需）
```

**📋 文件命名要求：**
- 文件名必须完全匹配（区分大小写）
- 患者名单文件必须是 `.xlsx` 格式
- 绩效数据文件必须是 `.csv` 格式
- 所有文件必须位于同一文件夹的根目录下
   
**✅ 正确示例：**
```
我的绩效数据/
├── patients.xlsx    ✓ 正确
├── 1.csv           ✓ 正确
├── 2.csv           ✓ 正确
├── 3.csv           ✓ 正确
└── icu.csv         ✓ 正确
```

**❌ 错误示例：**
```
我的绩效数据/
├── 患者名单.xlsx    ✗ 文件名错误
├── 第一季度.csv     ✗ 文件名错误
├── 子文件夹/
│   └── 1.csv       ✗ 文件位置错误
└── 1.xls           ✗ 文件格式错误
```

### 患者名单文件 (patients.xlsx)
| 列名 | 说明 | 必需 | 示例 |
|------|------|------|------|
| 患者姓名 | 患者的姓名 | ✓ | 张三 |
| 住院医生 | 负责的医生姓名 | ✓ | 李医生 |
| 科室 | 所属科室 | ○ | 呼吸内科 |
| 床号 | 床位号 | ○ | 101 |
| 住院号 | 住院编号 | ○ | 2025001 |

### 绩效数据文件 (CSV格式)
| 列名 | 说明 | 必需 | 示例 |
|------|------|------|------|
| xm | 患者姓名 | ✓ | 张三 |
| xz | 协助点数 | ✓ | 10.5 |
| zx | 执行点数 | ✓ | 25.0 |
| name | 项目名称 | ○ | 胸部CT |

**💡 提示：**
- 患者姓名必须在所有文件中保持一致，用于数据关联
- 数值列（xz、zx）必须是数字格式
- 如果您没有现成的数据文件，可以使用程序的"创建数据模板"功能生成标准格式的示例文件

## 🎯 使用指南

### 快速开始
1. **环境准备**
   ```bash
   python --version  # 确保Python 3.7+
   pip install -r requirements.txt
   ```

2. **启动程序**
   ```bash
   python main.py
   ```

3. **操作流程**
   - 选择包含数据文件的文件夹
   - 处理患者名单（自动去重）
   - 计算绩效数据
   - 导出Excel报告

### 医生管理
1. 在主界面点击"医生管理"按钮
2. 在弹出的对话框中管理医生和分组
3. 所有修改会自动保存到配置文件

### 常用操作
- **添加医生**: 点击"添加医生" → 输入姓名 → 选择分组
- **移动医生**: 双击医生 或 选择医生后点击"移动分组"
- **创建分组**: 点击"新建分组" → 输入分组名称
- **查看分组医生**: 点击左侧分组，右侧显示该分组医生

### 基本操作流程
1. **启动程序**: 运行 `python main.py`
2. **选择数据**: 点击"浏览"选择数据文件夹
3. **处理数据**: 点击"处理患者名单"进行去重
4. **计算绩效**: 点击"开始计算"处理所有数据
5. **生成报告**: 点击"导出Excel报告"生成结果

### 高级功能
- **数据模板生成**: 创建标准格式的示例文件
- **实时日志查看**: 监控处理过程和错误信息
- **数据预览**: 处理前后的数据对比查看
- **配置备份**: 自动创建带时间戳的备份文件

## ⚙️ 配置说明

### 医生分组规则
程序内置以下医生分组规则：

- **赖红琳组**: 赖红琳、李凡、陈小永
- **吴西雅组**: 廖丽军、吴西雅、吴海凤
- **童波组**: 童波、刘娜、唐斌
- **夏顺生组**: 梁莹、夏顺生、陈卫群
- **邹国明组**: 邹国明、周洪
- **其他组**: 其他未分组的医生

可通过医生管理界面或修改配置文件来调整分组。

### 配置文件
- `config.py` - 系统配置参数
- `医生分组配置.json` - 医生分组持久化数据
- `医生名单.md` - 医生名单文档（向后兼容）

## � 输出报告

生成的Excel报告包含以下工作表：

1. **医生个人绩效** - 每个医生的详细绩效统计
2. **医疗组汇总** - 按医疗组的绩效汇总
3. **详细绩效数据** - 所有原始计算结果
4. **数据处理日志** - 处理过程的详细记录
5. **报告汇总** - 整体统计信息

## 🔧 技术架构

### 核心技术栈
- **Python 3.7+** - 主要开发语言
- **tkinter** - 图形用户界面
- **pandas** - 数据处理和分析
- **openpyxl** - Excel文件操作
- **JSON** - 配置数据持久化

### 设计模式
- **MVC架构** - 分离界面、业务逻辑和数据
- **模块化设计** - 功能模块独立，易于维护
- **配置外置** - 业务规则可配置，提高灵活性

### 数据流程
```
数据输入 → 数据验证 → 患者去重 → 绩效计算 → 医生分组 → 报告生成 → 结果输出
```

## 📊 系统功能流程图

### 整体业务流程
```mermaid
flowchart TD
    A[程序启动] --> B[初始化系统]
    B --> C[加载医生分组配置]
    C --> D[显示主界面]
    D --> E[选择数据文件夹]
    E --> F{验证文件结构}
    F -->|文件缺失| G[显示错误信息]
    F -->|验证通过| H[加载患者数据]
    H --> I[分析患者数据]
    I --> J{发现重复患者?}
    J -->|是| K[用户选择去重策略]
    J -->|否| L[数据预处理完成]
    K --> M[执行去重处理]
    M --> L
    L --> N[加载绩效数据]
    N --> O[验证绩效数据格式]
    O --> P[执行绩效计算]
    P --> Q[生成汇总统计]
    Q --> R[生成Excel报告]
    R --> S[显示完成信息]
    S --> T[用户可选择查看报告]
    
    U[医生管理] --> V[医生分组操作]
    V --> W[保存配置]
    W --> X[更新主界面显示]
    
    G --> D
    T --> D
    X --> D
    
    style A fill:#e1f5fe
    style S fill:#c8e6c9
    style G fill:#ffcdd2
```

### 数据处理详细流程
```mermaid
flowchart TD
    A[读取patients.xlsx] --> B[检查必需列]
    B --> C{列名是否正确?}
    C -->|否| D[提供列名映射建议]
    C -->|是| E[分析数据质量]
    E --> F[检测重复记录]
    F --> G{存在重复?}
    G -->|是| H[显示重复统计]
    G -->|否| I[数据清理完成]
    H --> J[用户选择去重策略]
    J --> K[keep_first: 保留首次出现]
    J --> L[keep_last: 保留最后出现]
    J --> M[remove_all: 删除所有重复]
    K --> N[执行去重]
    L --> N
    M --> N
    N --> I
    I --> O[加载绩效CSV文件]
    O --> P[1.csv - 第一季度]
    O --> Q[2.csv - 第二季度]
    O --> R[3.csv - 第三季度]
    O --> S[icu.csv - ICU数据]
    P --> T[验证绩效数据格式]
    Q --> T
    R --> T
    S --> T
    T --> U{格式是否正确?}
    U -->|否| V[记录错误日志]
    U -->|是| W[患者-绩效数据匹配]
    W --> X[按患者姓名关联数据]
    X --> Y[计算个人绩效汇总]
    Y --> Z[按医生分组统计]
    Z --> AA[生成最终报告数据]
    
    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style V fill:#ffebee
    style AA fill:#f3e5f5
```

### 系统架构图
```mermaid
graph TB
    subgraph "用户界面层 (GUI Layer)"
        A[main_window.py<br/>主窗口]
        B[components.py<br/>通用组件]
        C[doctor_management_dialog.py<br/>医生管理对话框]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        D[doctor_manager.py<br/>医生分组管理]
        E[patient_processor.py<br/>患者数据处理]
        F[performance_calculator.py<br/>绩效计算引擎]
        G[report_generator.py<br/>报告生成器]
    end
    
    subgraph "工具支持层 (Utility Layer)"
        H[logger.py<br/>日志管理]
        I[file_utils.py<br/>文件操作工具]
        J[config.py<br/>配置管理]
    end
    
    subgraph "数据层 (Data Layer)"
        K[patients.xlsx<br/>患者名单]
        L[1.csv, 2.csv, 3.csv<br/>季度绩效数据]
        M[icu.csv<br/>ICU绩效数据]
        N[医生分组配置.json<br/>医生分组配置]
        O[output/*.xlsx<br/>Excel报告]
        P[logs/*.log<br/>日志文件]
    end
    
    subgraph "程序入口"
        Q[main.py<br/>程序启动]
    end
    
    Q --> A
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    
    C --> D
    E --> D
    F --> D
    F --> E
    G --> F
    
    D --> H
    E --> H
    F --> H
    G --> H
    
    D --> I
    E --> I
    F --> I
    G --> I
    
    D --> J
    E --> J
    F --> J
    G --> J
    A --> J
    
    D -.-> N
    E -.-> K
    F -.-> L
    F -.-> M
    G -.-> O
    H -.-> P
    
    style A fill:#bbdefb
    style D fill:#c8e6c9
    style H fill:#fff3e0
    style K fill:#f3e5f5
    style Q fill:#ffcdd2
```

### 医生分组管理流程
```mermaid
flowchart TD
    A[医生管理界面] --> B[加载现有分组配置]
    B --> C[显示分组树结构]
    C --> D{用户操作}
    D --> E[添加医生]
    D --> F[删除医生]
    D --> G[移动医生]
    D --> H[新建分组]
    D --> I[删除分组]
    D --> J[重命名分组]
    
    E --> K[输入医生姓名]
    K --> L[选择目标分组]
    L --> M[更新配置数据]
    
    F --> N[确认删除操作]
    N --> M
    
    G --> O[选择目标分组]
    O --> M
    
    H --> P[输入分组名称]
    P --> M
    
    I --> Q[确认删除分组]
    Q --> R[移动分组内医生到"其他组"]
    R --> M
    
    J --> S[输入新分组名称]
    S --> M
    
    M --> T[保存到JSON文件]
    T --> U[创建备份文件]
    U --> V[更新界面显示]
    V --> W[刷新主窗口医生信息]
    
    style A fill:#e1f5fe
    style M fill:#fff3e0
    style T fill:#e8f5e8
    style W fill:#f3e5f5
```

## 🐛 故障排除

### 常见问题

**Q: 程序启动失败**
A: 检查Python版本和依赖包是否正确安装

**Q: 文件夹验证失败**
A: 确保数据文件夹包含所有必需的文件，文件名大小写正确

**Q: 患者数据处理失败**
A: 检查patients.xlsx文件格式，确保包含"患者姓名"和"住院医生"列

**Q: 绩效计算无结果**
A: 检查CSV文件格式，确保患者姓名在各文件中匹配

**Q: Excel报告打开失败**
A: 确保系统已安装Excel或其他支持.xlsx格式的软件

### 日志分析
查看 `logs/` 目录中的日志文件，可以获得详细的错误信息和处理过程。

## 📝 更新日志

### v1.0.0 (2025-05-24)
- 初始版本发布
- 基于原VBA代码完全重构
- 实现现代化GUI界面
- 支持患者数据去重处理
- 支持多数据源绩效计算
- 支持Excel报告生成

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本程序基于原VBA代码重构，保持了原有的业务逻辑和计算规则，同时提供了更好的用户体验和错误处理机制。
