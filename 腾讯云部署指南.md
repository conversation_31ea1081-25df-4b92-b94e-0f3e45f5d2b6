# 医院科室绩效计算器 - 腾讯云部署指南

## 🚀 概述

本文档详细说明如何将医院科室绩效计算器Web版本部署到腾讯云，包括服务器配置、数据库设置、域名绑定和SSL证书配置。

## 📋 部署架构

```
用户浏览器
    ↓
CDN (腾讯云CDN)
    ↓
负载均衡器 (腾讯云CLB)
    ↓
Web服务器 (腾讯云CVM)
    ↓
数据库 (腾讯云MySQL)
    ↓
文件存储 (腾讯云COS)
```

## 🛠️ 准备工作

### 1. 腾讯云账号准备
- 注册腾讯云账号
- 完成实名认证
- 开通以下服务：
  - 云服务器 CVM
  - 云数据库 MySQL
  - 对象存储 COS
  - 负载均衡 CLB（可选）
  - 内容分发网络 CDN（可选）

### 2. 本地准备
- 确保项目代码已经完成Web化改造
- 准备域名（建议）
- 准备SSL证书（如需HTTPS）

## 📡 第一步：创建云服务器 (CVM)

### 1.1 选择服务器配置
**推荐配置：**
- **实例规格**: 标准型S5 (2核4GB) 或更高
- **操作系统**: Ubuntu 20.04 LTS
- **硬盘**: 系统盘40GB + 数据盘20GB
- **网络**: 分配公网IP，带宽按需选择

### 1.2 安全组配置
```bash
# 开放端口
22   (SSH)
80   (HTTP)
443  (HTTPS)
5000 (Flask应用，后续可关闭)
```

### 1.3 连接服务器
```bash
ssh ubuntu@your-server-ip
```

## 🗄️ 第二步：创建云数据库 (MySQL)

### 2.1 创建MySQL实例
- **版本**: MySQL 8.0
- **配置**: 1核1GB（可按需升级）
- **存储**: 20GB SSD云硬盘
- **网络**: 选择与CVM相同的VPC

### 2.2 配置数据库
```sql
-- 连接到MySQL实例
mysql -h your-mysql-host -u root -p

-- 创建应用数据库
CREATE DATABASE performance_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'app_user'@'%' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON performance_calculator.* TO 'app_user'@'%';
FLUSH PRIVILEGES;
```

## 📁 第三步：配置对象存储 (COS)

### 3.1 创建存储桶
```bash
# 创建存储桶
桶名称: performance-calculator-files
地域: 与CVM相同地域
访问权限: 私有读写
```

### 3.2 获取访问密钥
- 前往访问管理 → API密钥管理
- 创建密钥，记录SecretId和SecretKey

## 🖥️ 第四步：服务器环境配置

### 4.1 更新系统和安装基础软件
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y python3 python3-pip python3-venv nginx mysql-client git

# 安装Node.js (用于前端资源管理)
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 4.2 安装和配置Redis
```bash
# 安装Redis
sudo apt install redis-server -y

# 配置Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# 测试Redis
redis-cli ping  # 应该返回PONG
```

### 4.3 配置防火墙
```bash
# 配置ufw防火墙
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 📦 第五步：部署应用

### 5.1 上传代码
```bash
# 创建应用目录
sudo mkdir -p /opt/performance-calculator
sudo chown ubuntu:ubuntu /opt/performance-calculator

# 上传代码到服务器
# 方法1: 使用git
cd /opt/performance-calculator
git clone https://github.com/your-username/performance-calculator.git .

# 方法2: 使用scp上传
# 在本地执行:
# scp -r /path/to/project/* ubuntu@your-server-ip:/opt/performance-calculator/
```

### 5.2 创建Python虚拟环境
```bash
cd /opt/performance-calculator

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r web_requirements.txt
```

### 5.3 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

修改`.env`文件内容：
```bash
FLASK_ENV=production
SECRET_KEY=your-super-secret-production-key
DATABASE_URL=mysql+pymysql://app_user:strong_password_here@your-mysql-host:3306/performance_calculator
REDIS_URL=redis://localhost:6379/0
TENCENTCLOUD_SECRET_ID=your-secret-id
TENCENTCLOUD_SECRET_KEY=your-secret-key
COS_BUCKET=performance-calculator-files
COS_REGION=ap-beijing
```

### 5.4 初始化数据库
```bash
# 激活虚拟环境
source venv/bin/activate

# 创建数据库表
python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Database tables created successfully!')
"
```

### 5.5 创建系统服务
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/performance-calculator.service
```

文件内容：
```ini
[Unit]
Description=Performance Calculator Web Application
After=network.target

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/performance-calculator
Environment=PATH=/opt/performance-calculator/venv/bin
ExecStart=/opt/performance-calculator/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 --timeout 300 app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable performance-calculator
sudo systemctl start performance-calculator

# 检查服务状态
sudo systemctl status performance-calculator
```

### 5.6 配置Celery后台任务
```bash
# 创建Celery服务文件
sudo nano /etc/systemd/system/celery-performance.service
```

文件内容：
```ini
[Unit]
Description=Celery Worker for Performance Calculator
After=network.target

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/performance-calculator
Environment=PATH=/opt/performance-calculator/venv/bin
ExecStart=/opt/performance-calculator/venv/bin/celery -A app.celery worker --loglevel=info
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动Celery：
```bash
sudo systemctl enable celery-performance
sudo systemctl start celery-performance
sudo systemctl status celery-performance
```

## 🌐 第六步：配置Nginx反向代理

### 6.1 安装和配置Nginx
```bash
# Nginx应该已经安装，如果没有：
sudo apt install nginx -y

# 创建站点配置
sudo nano /etc/nginx/sites-available/performance-calculator
```

配置文件内容：
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # 替换为你的域名
    
    # 重定向到HTTPS（如果配置了SSL）
    # return 301 https://$server_name$request_uri;
    
    # 静态文件
    location /static {
        alias /opt/performance-calculator/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 上传文件大小限制
    client_max_body_size 50M;
    
    # 反向代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }
}
```

启用站点：
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/performance-calculator /etc/nginx/sites-enabled/

# 删除默认站点
sudo rm /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 第七步：配置SSL证书（HTTPS）

### 7.1 使用Let's Encrypt免费SSL证书
```bash
# 安装certbot
sudo apt install snapd
sudo snap install core; sudo snap refresh core
sudo snap install --classic certbot

# 创建软链接
sudo ln -s /snap/bin/certbot /usr/bin/certbot

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 7.2 更新Nginx配置支持HTTPS
```bash
sudo nano /etc/nginx/sites-available/performance-calculator
```

添加HTTPS配置：
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    
    # 其他配置与HTTP相同...
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

重新加载Nginx：
```bash
sudo nginx -t
sudo systemctl reload nginx
```

## 🎛️ 第八步：配置监控和日志

### 8.1 配置日志轮转
```bash
sudo nano /etc/logrotate.d/performance-calculator
```

内容：
```
/opt/performance-calculator/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        sudo systemctl reload performance-calculator
    endscript
}
```

### 8.2 设置监控脚本
```bash
# 创建监控脚本
nano /opt/performance-calculator/monitor.sh
```

脚本内容：
```bash
#!/bin/bash

# 检查应用是否运行
if ! systemctl is-active --quiet performance-calculator; then
    echo "Performance Calculator is down! Restarting..."
    sudo systemctl restart performance-calculator
fi

# 检查Nginx是否运行
if ! systemctl is-active --quiet nginx; then
    echo "Nginx is down! Restarting..."
    sudo systemctl restart nginx
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%! Please check."
fi
```

设置定时任务：
```bash
chmod +x /opt/performance-calculator/monitor.sh

# 添加到crontab
crontab -e

# 添加以下行（每5分钟检查一次）
*/5 * * * * /opt/performance-calculator/monitor.sh >> /opt/performance-calculator/logs/monitor.log 2>&1
```

## 🔧 第九步：性能优化

### 9.1 配置Redis持久化
```bash
sudo nano /etc/redis/redis.conf

# 取消注释并修改以下配置
save 900 1
save 300 10
save 60 10000
```

重启Redis：
```bash
sudo systemctl restart redis-server
```

### 9.2 优化MySQL配置
```bash
# 在腾讯云MySQL控制台调整参数：
innodb_buffer_pool_size = 75% of available memory
max_connections = 500
query_cache_size = 64M
```

### 9.3 配置Nginx缓存
在Nginx配置中添加：
```nginx
# 在http块中添加
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=1g 
                 inactive=60m use_temp_path=off;

# 在location块中添加
location /static {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🚦 第十步：测试部署

### 10.1 功能测试
1. 访问 `https://your-domain.com`
2. 注册新账号
3. 登录系统
4. 上传测试文件
5. 执行绩效计算
6. 下载结果报告

### 10.2 性能测试
```bash
# 安装压力测试工具
sudo apt install apache2-utils

# 简单压力测试
ab -n 100 -c 10 https://your-domain.com/
```

### 10.3 检查日志
```bash
# 查看应用日志
tail -f /opt/performance-calculator/logs/performance_app.log

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看系统服务状态
sudo systemctl status performance-calculator
sudo systemctl status celery-performance
sudo systemctl status nginx
```

## 🔐 第十一步：安全配置

### 11.1 配置防火墙规则
```bash
# 只允许必要端口
sudo ufw reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 11.2 配置SSH安全
```bash
sudo nano /etc/ssh/sshd_config

# 修改以下配置
PermitRootLogin no
PasswordAuthentication no  # 使用密钥登录
Port 2222  # 修改默认端口

sudo systemctl restart ssh
```

### 11.3 定期备份
```bash
# 创建备份脚本
nano /opt/performance-calculator/backup.sh
```

脚本内容：
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h your-mysql-host -u app_user -p performance_calculator > $BACKUP_DIR/db_backup_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz /opt/performance-calculator/uploads

# 删除7天前的备份
find $BACKUP_DIR -name "*backup*" -mtime +7 -delete

echo "Backup completed at $DATE"
```

设置定时备份：
```bash
chmod +x /opt/performance-calculator/backup.sh

# 添加到crontab（每天凌晨2点备份）
crontab -e
0 2 * * * /opt/performance-calculator/backup.sh >> /opt/performance-calculator/logs/backup.log 2>&1
```

## 📱 第十二步：配置CDN加速（可选）

### 12.1 创建CDN分发
1. 登录腾讯云控制台
2. 前往CDN服务
3. 添加域名：`cdn.your-domain.com`
4. 源站类型：自有源
5. 源站地址：你的服务器IP

### 12.2 配置缓存规则
```
# 静态资源缓存30天
/static/*     30天
*.css         30天
*.js          30天
*.png         30天
*.jpg         30天

# API接口不缓存
/api/*        不缓存
```

## 📊 第十三步：监控告警

### 13.1 配置腾讯云监控
1. 前往云监控控制台
2. 创建告警策略
3. 监控指标：
   - CPU使用率 > 80%
   - 内存使用率 > 80%
   - 磁盘使用率 > 80%
   - 网络带宽

### 13.2 应用级监控
```bash
# 安装应用监控
pip install flask-monitoring-dashboard

# 在app.py中添加
from flask_monitoring_dashboard import bind
bind(app)
```

## 🎯 部署清单

### ✅ 部署前检查
- [ ] 腾讯云服务已开通
- [ ] 域名已备案（如在中国大陆）
- [ ] SSL证书已准备
- [ ] 数据库已创建
- [ ] 代码已上传

### ✅ 部署完成检查
- [ ] Web应用正常访问
- [ ] 用户注册/登录功能正常
- [ ] 文件上传功能正常
- [ ] 绩效计算功能正常
- [ ] 报告下载功能正常
- [ ] SSL证书有效
- [ ] 监控告警已配置
- [ ] 备份策略已实施

## 🛠️ 故障排除

### 常见问题

**Q: 应用无法启动**
```bash
# 检查日志
sudo journalctl -u performance-calculator -f
# 检查端口占用
sudo netstat -tlnp | grep :5000
```

**Q: 数据库连接失败**
```bash
# 测试数据库连接
mysql -h your-mysql-host -u app_user -p
# 检查防火墙规则
```

**Q: 文件上传失败**
```bash
# 检查目录权限
ls -la /opt/performance-calculator/uploads
# 检查Nginx配置
sudo nginx -t
```

**Q: SSL证书问题**
```bash
# 检查证书有效期
sudo certbot certificates
# 手动续期
sudo certbot renew
```

## 📈 扩展和优化

### 高可用架构
- 使用腾讯云CLB负载均衡
- 部署多台Web服务器
- 配置MySQL主从复制
- 使用Redis集群

### 性能监控
- 接入腾讯云APM
- 配置日志分析
- 设置性能基线

## 🎉 部署完成

恭喜！您已成功将医院科室绩效计算器部署到腾讯云。现在用户可以通过Web浏览器安全地访问和使用这个现代化的绩效管理系统。

如需技术支持，请查看项目文档或联系系统管理员。