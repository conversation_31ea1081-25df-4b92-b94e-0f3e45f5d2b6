#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web服务器脚本
"""

import os
import sys
import subprocess
import time
import signal
import threading
import webbrowser
from urllib.request import urlopen
from urllib.error import URLError

def check_port(port=5000):
    """检查端口是否被占用"""
    import socket
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    return result == 0

def wait_for_server(url, timeout=30):
    """等待服务器启动"""
    print(f"⏳ 等待服务器启动... (最多等待{timeout}秒)")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            urlopen(url, timeout=2)
            return True
        except URLError:
            time.sleep(1)
            print(".", end="", flush=True)
    
    print("\n❌ 服务器启动超时")
    return False

def open_browser(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    print(f"🌐 在浏览器中打开: {url}")
    webbrowser.open(url)

def start_server():
    """启动Web服务器"""
    print("🏥 医院科室绩效计算器 - Web服务器启动")
    print("=" * 50)
    
    # 检查虚拟环境
    venv_path = "web_venv/bin/activate"
    if not os.path.exists(venv_path):
        print("❌ 虚拟环境不存在，请先运行: python3 -m venv web_venv")
        return False
    
    # 设置端口
    port = 5000
    if check_port(port):
        print(f"⚠️ 端口 {port} 已被占用，尝试使用端口 5001")
        port = 5001
        if check_port(port):
            print(f"❌ 端口 {port} 也被占用，请手动释放端口或重启系统")
            return False
    
    url = f"http://localhost:{port}"
    
    print(f"🚀 正在启动服务器...")
    print(f"📱 访问地址: {url}")
    print(f"🔑 默认管理员: admin / admin123")
    print(f"🧪 测试数据: test_data/ 目录")
    print(f"❌ 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser, args=(url, 3))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 构建启动命令
        if sys.platform == "win32":
            cmd = f"web_venv\\Scripts\\activate && python app.py --port {port}"
            process = subprocess.Popen(cmd, shell=True, cwd=os.getcwd())
        else:
            cmd = f"source web_venv/bin/activate && python app.py"
            # 修改app.py中的端口设置
            if port != 5000:
                os.environ['FLASK_PORT'] = str(port)
            
            process = subprocess.Popen(cmd, shell=True, cwd=os.getcwd(), 
                                     executable='/bin/bash')
        
        # 等待服务器启动
        if wait_for_server(url):
            print("✅ 服务器启动成功！")
            print(f"🌐 请在浏览器中访问: {url}")
            
            # 等待进程结束
            process.wait()
        else:
            print("❌ 服务器启动失败")
            process.terminate()
            return False
            
    except KeyboardInterrupt:
        print("\n👋 收到停止信号，正在关闭服务器...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        print("✅ 服务器已停止")
        return True
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    start_server()