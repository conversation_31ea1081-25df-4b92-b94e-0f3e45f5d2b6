@echo off
chcp 65001 >nul
echo 医院科室绩效计算器启动脚本
echo ================================

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show pandas >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动绩效计算器...
python main.py

if errorlevel 1 (
    echo 程序异常退出
    pause
)
