#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院科室绩效计算器 - 主程序入口
基于原VBA代码重构的现代化Python版本

作者: AI Assistant
创建时间: 2025-05-24
版本: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.main_window import PerformanceCalculatorGUI
from src.utils.logger import setup_logger

def main():
    """主程序入口"""
    try:
        print("🚀 正在启动医院科室绩效计算器...")
        print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 设置日志
        print("📝 初始化日志系统...")
        logger = setup_logger()
        logger.info("=" * 60)
        logger.info("医院科室绩效计算器启动")
        logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        # 创建主窗口
        print("🖥️  创建主窗口...")
        root = tk.Tk()

        # 确保窗口显示在前台
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)

        print("🔧 初始化应用程序...")
        app = PerformanceCalculatorGUI(root)

        print("✅ 启动完成！主窗口应该已经显示")
        print("📋 您可以开始使用医生管理功能了")

        # 启动GUI事件循环
        root.mainloop()

        logger.info("程序正常退出")
        print("👋 程序已退出")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(0)

    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}"
        print(f"❌ {error_msg}")

        # 打印详细错误信息
        import traceback
        traceback.print_exc()

        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", error_msg)
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main()
