#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整测试脚本
"""

import os
import sys
import requests
import subprocess
import time
import signal

def run_final_test():
    print("🏥 医院科室绩效计算器 - 最终测试")
    print("=" * 60)
    
    # 测试1: 检查环境
    print("📋 1. 环境检查...")
    required_files = [
        'app.py',
        'web_requirements.txt',
        'templates/base.html',
        'templates/index.html', 
        'templates/login.html',
        'templates/register.html',
        'templates/dashboard.html',
        'templates/calculate.html',
        'test_data/patients.xlsx',
        'test_data/1.csv',
        'test_data/2.csv',
        'test_data/3.csv',
        'test_data/icu.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 测试2: 数据库功能
    print("\n🗄️ 2. 数据库功能测试...")
    try:
        from app import app, db, User
        with app.app_context():
            # 检查用户表
            users = User.query.all()
            print(f"   ✅ 数据库连接正常，用户数: {len(users)}")
            
            # 检查管理员用户
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("   ✅ 管理员用户存在")
            else:
                print("   ❌ 管理员用户不存在")
                return False
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return False
    
    # 测试3: 应用导入
    print("\n📦 3. 应用导入测试...")
    try:
        from app import app
        print("   ✅ Flask 应用导入成功")
        
        # 检查路由
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        expected_routes = ['/', '/login', '/register', '/dashboard', '/calculate']
        
        for route in expected_routes:
            if route in routes:
                print(f"   ✅ 路由 {route} 已注册")
            else:
                print(f"   ❌ 路由 {route} 未注册")
                return False
                
    except Exception as e:
        print(f"   ❌ 应用导入失败: {e}")
        return False
    
    # 测试4: 模板渲染
    print("\n🎨 4. 模板渲染测试...")
    try:
        with app.test_client() as client:
            test_routes = [
                ('/', '主页'),
                ('/login', '登录页'),
                ('/register', '注册页')
            ]
            
            for route, name in test_routes:
                response = client.get(route)
                if response.status_code == 200:
                    print(f"   ✅ {name} 渲染正常")
                else:
                    print(f"   ❌ {name} 渲染失败: {response.status_code}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ 模板渲染测试失败: {e}")
        return False
    
    # 测试5: 用户认证
    print("\n🔐 5. 用户认证测试...")
    try:
        with app.test_client() as client:
            # 测试登录
            response = client.post('/login', data={
                'username': 'admin',
                'password': 'admin123'
            }, follow_redirects=False)
            
            if response.status_code in [200, 302]:  # 成功或重定向
                print("   ✅ 管理员登录功能正常")
            else:
                print(f"   ❌ 登录失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ 用户认证测试失败: {e}")
        return False
    
    # 测试6: 文件处理
    print("\n📁 6. 文件处理测试...")
    try:
        import pandas as pd
        
        # 测试读取患者数据
        df = pd.read_excel('test_data/patients.xlsx')
        print(f"   ✅ 患者数据: {len(df)} 条记录")
        
        # 测试读取绩效数据
        total_records = 0
        for csv_file in ['1.csv', '2.csv', '3.csv', 'icu.csv']:
            df = pd.read_csv(f'test_data/{csv_file}')
            total_records += len(df)
            print(f"   ✅ {csv_file}: {len(df)} 条记录")
        
        print(f"   ✅ 总绩效记录: {total_records} 条")
        
    except Exception as e:
        print(f"   ❌ 文件处理测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！系统已就绪！")
    
    print("\n🚀 启动说明:")
    print("1. 激活虚拟环境:")
    print("   source web_venv/bin/activate")
    print("\n2. 启动Web应用:")
    print("   python app.py")
    print("\n3. 在浏览器中访问:")
    print("   http://localhost:5000")
    print("\n4. 使用以下账号登录:")
    print("   用户名: admin")
    print("   密码: admin123")
    print("\n5. 测试数据位于:")
    print("   test_data/ 目录")
    
    print("\n📋 功能测试建议:")
    print("• 注册新用户账号")
    print("• 上传测试数据文件进行绩效计算")
    print("• 测试医生管理功能")
    print("• 下载生成的Excel报告")
    
    return True

if __name__ == "__main__":
    # 切换到脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    success = run_final_test()
    if success:
        print("\n✨ 测试完成！您可以开始使用Web版绩效计算器了！")
    else:
        print("\n❌ 测试失败！请检查错误信息并修复问题。")
    
    sys.exit(0 if success else 1)