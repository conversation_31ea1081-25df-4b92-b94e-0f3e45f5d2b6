#!/bin/bash

# 医院科室绩效计算器 - 本地测试启动脚本

set -e  # 遇到错误时退出

echo "🏥 医院科室绩效计算器 - 本地测试环境启动"
echo "============================================"

# 检查Python版本
echo "📋 检查Python环境..."
python3 --version || {
    echo "❌ 错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
}

# 创建虚拟环境（如果不存在）
if [ ! -d "web_venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv web_venv
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source web_venv/bin/activate

# 安装依赖
echo "📦 安装/更新依赖包..."
pip install --upgrade pip
pip install -r web_requirements.txt

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p uploads output logs test_data

# 复制环境变量文件（如果不存在）
if [ ! -f ".env" ]; then
    echo "⚙️ 创建环境变量文件..."
    cp .env.example .env
    echo "✏️ 请编辑 .env 文件配置本地测试参数"
fi

# 初始化数据库
echo "🗄️ 初始化数据库..."
python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('✅ 数据库表创建成功！')
" || {
    echo "❌ 数据库初始化失败"
    exit 1
}

# 创建测试数据
echo "📊 创建测试数据..."
cat > create_test_data.py << 'EOF'
import pandas as pd
import os

print("创建测试数据文件...")

# 确保测试目录存在
os.makedirs('test_data', exist_ok=True)

# 创建患者测试数据
patients_data = {
    '患者姓名': ['张三', '李四', '王五', '赵六', '陈七', '刘八', '黄九', '朱十'],
    '住院医生': ['赖红琳', '吴西雅', '童波', '夏顺生', '邹国明', '李凡', '陈小永', '廖丽军'],
    '科室': ['呼吸内科'] * 8,
    '床号': ['101', '102', '103', '104', '105', '106', '107', '108'],
    '住院号': ['2025001', '2025002', '2025003', '2025004', '2025005', '2025006', '2025007', '2025008']
}

df_patients = pd.DataFrame(patients_data)
df_patients.to_excel('test_data/patients.xlsx', index=False)
print("✅ 创建 patients.xlsx")

# 创建绩效数据
def create_performance_csv(filename, patients, base_xz=10, base_zx=25):
    performance_data = {
        'xm': patients,
        'xz': [base_xz + i*2 for i in range(len(patients))],  # 协助点数
        'zx': [base_zx + i*3 for i in range(len(patients))],  # 执行点数
        'name': [f'检查项目{i+1}' for i in range(len(patients))],
    }
    df = pd.DataFrame(performance_data)
    df.to_csv(f'test_data/{filename}', index=False, encoding='utf-8')
    print(f"✅ 创建 {filename}")

# 创建各个病区的测试数据
create_performance_csv('1.csv', ['张三', '李四'], 10, 25)    # 一病区
create_performance_csv('2.csv', ['王五', '赵六'], 12, 28)    # 二病区  
create_performance_csv('3.csv', ['陈七', '刘八'], 15, 30)    # 三病区
create_performance_csv('icu.csv', ['黄九', '朱十'], 20, 35)  # 呼吸ICU

print("🎉 所有测试数据创建完成！")
print("\n📁 测试文件列表：")
for file in sorted(os.listdir('test_data')):
    print(f"   test_data/{file}")
EOF

python create_test_data.py
rm create_test_data.py

# 检查端口是否被占用
PORT=5000
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ 端口 $PORT 已被占用，尝试使用端口 5001"
    PORT=5001
fi

echo ""
echo "🎉 本地测试环境准备完成！"
echo ""
echo "📋 测试清单："
echo "   ✅ Python虚拟环境已创建并激活"
echo "   ✅ 依赖包已安装"
echo "   ✅ 数据库已初始化"
echo "   ✅ 测试数据已生成"
echo "   ✅ 必要目录已创建"
echo ""
echo "🚀 启动Web应用..."
echo "📱 应用将在 http://localhost:$PORT 启动"
echo "🔑 默认管理员账号: admin / admin123"
echo ""
echo "🧪 开始测试："
echo "   1. 在浏览器打开 http://localhost:$PORT"
echo "   2. 注册新用户或使用管理员账号登录"
echo "   3. 上传 test_data/ 目录中的测试文件"
echo "   4. 执行绩效计算测试"
echo ""
echo "❌ 按 Ctrl+C 停止服务器"
echo "============================================"

# 启动Flask应用
if [ $PORT = 5001 ]; then
    sed -i.bak "s/port=5000/port=5001/" app.py
fi

python app.py