{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary">
                <i class="bi bi-hospital"></i> 医院科室绩效计算器
            </h1>
            <p class="lead text-muted">现代化的医院绩效管理系统，提供高效、准确的绩效计算和分析</p>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-speedometer2 text-primary" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">高效处理</h5>
                        <p class="card-text">自动化数据处理，相比原VBA系统效率提升80%</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">数据安全</h5>
                        <p class="card-text">用户隔离、权限控制，确保数据安全可靠</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-graph-up text-info" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">智能分析</h5>
                        <p class="card-text">多维度统计分析，生成详细绩效报告</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <h3 class="card-title">核心功能</h3>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>患者名单智能去重</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>多数据源绩效计算</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>医生分组管理</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Excel报告生成</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>用户权限管理</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>任务历史记录</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>实时处理状态</li>
                            <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>云端数据存储</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg me-3">
                    <i class="bi bi-speedometer2"></i> 进入工作台
                </a>
                <a href="{{ url_for('calculate') }}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-calculator"></i> 开始计算
                </a>
            {% else %}
                <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg me-3">
                    <i class="bi bi-box-arrow-in-right"></i> 立即登录
                </a>
                <a href="{{ url_for('register') }}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-person-plus"></i> 注册账号
                </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row mt-5">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h4 class="card-title">系统要求</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>支持的文件格式：</h6>
                        <ul>
                            <li>患者名单：Excel文件（.xlsx）</li>
                            <li>绩效数据：CSV文件（.csv）</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>必需文件：</h6>
                        <ul>
                            <li>patients.xlsx - 患者名单</li>
                            <li>1.csv, 2.csv, 3.csv - 病区数据</li>
                            <li>icu.csv - 呼吸ICU数据</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}