{% extends "base.html" %}

{% block title %}注册 - 医院科室绩效计算器{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="bi bi-person-plus text-primary" style="font-size: 4rem;"></i>
                    <h3 class="mt-2">注册账号</h3>
                    <p class="text-muted">创建您的绩效计算器账号</p>
                </div>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                <small class="form-text text-muted">用于登录的唯一标识</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <small class="form-text text-muted">用于接收通知和找回密码</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="doctor_name" class="form-label">医生姓名</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                            <input type="text" class="form-control" id="doctor_name" name="doctor_name" placeholder="请输入您的真实姓名">
                        </div>
                        <small class="form-text text-muted">可选，用于关联绩效数据</small>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                        </div>
                        <small class="form-text text-muted">密码至少6位字符</small>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agree_terms" required>
                        <label class="form-check-label" for="agree_terms">
                            我已阅读并同意 <a href="#" class="text-decoration-none">服务条款</a> 和 <a href="#" class="text-decoration-none">隐私政策</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-person-plus"></i> 注册账号
                    </button>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-0">已有账号？</p>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-sm mt-2">
                        <i class="bi bi-box-arrow-in-right"></i> 立即登录
                    </a>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-muted">
                注册即表示您同意我们的服务条款和数据处理政策
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('密码不匹配');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('change', validatePasswords);
    confirmPassword.addEventListener('keyup', validatePasswords);
});
</script>
{% endblock %}