{% extends "base.html" %}

{% block title %}绩效计算 - 医院科室绩效计算器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-calculator"></i> 绩效计算</h2>
                <p class="text-muted mb-0">上传数据文件并启动绩效计算任务</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回工作台
                </a>
            </div>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="calculateForm">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-cloud-upload"></i> 上传数据文件
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>文件要求：</strong> 请上传以下5个必需文件：patients.xlsx、1.csv、2.csv、3.csv、icu.csv
                    </div>

                    <div class="upload-area" id="uploadArea">
                        <i class="bi bi-cloud-upload text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">拖拽文件到这里或点击选择</h5>
                        <p class="text-muted">支持多文件同时上传</p>
                        <input type="file" class="d-none" id="fileInput" name="files" multiple 
                               accept=".xlsx,.csv" required>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="bi bi-folder2-open"></i> 选择文件
                        </button>
                    </div>

                    <div id="fileList" class="mt-3" style="display: none;">
                        <h6>已选择的文件：</h6>
                        <div class="list-group" id="selectedFiles"></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear"></i> 计算设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskName" class="form-label">任务名称</label>
                                <input type="text" class="form-control" id="taskName" name="task_name" 
                                       value="绩效计算_{{ current_time.strftime('%Y-%m-%d_%H-%M-%S') }}" required>
                                <small class="form-text text-muted">为本次计算任务起个名字</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duplicateStrategy" class="form-label">重复数据处理策略</label>
                                <select class="form-select" id="duplicateStrategy" name="duplicate_strategy">
                                    <option value="keep_first">保留首次出现的记录</option>
                                    <option value="keep_last">保留最后出现的记录</option>
                                    <option value="remove_all">删除所有重复记录</option>
                                </select>
                                <small class="form-text text-muted">遇到重复患者数据时的处理方式</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="generateReport" checked>
                            <label class="form-check-label" for="generateReport">
                                自动生成Excel报告
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotification">
                            <label class="form-check-label" for="emailNotification">
                                完成后发送邮件通知（如已配置邮箱）
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                        <i class="bi bi-play-circle"></i> 开始计算
                    </button>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-question-circle"></i> 使用说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="step-item mb-3">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h6>准备文件</h6>
                            <p class="small text-muted">确保您有以下5个文件：患者名单（patients.xlsx）和4个绩效数据文件（1.csv、2.csv、3.csv、icu.csv）</p>
                        </div>
                    </div>

                    <div class="step-item mb-3">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h6>上传文件</h6>
                            <p class="small text-muted">可以拖拽文件到上传区域，或点击选择按钮批量上传</p>
                        </div>
                    </div>

                    <div class="step-item mb-3">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h6>配置参数</h6>
                            <p class="small text-muted">设置任务名称和重复数据处理策略</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h6>开始计算</h6>
                            <p class="small text-muted">点击开始计算，系统将自动处理数据并生成报告</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-file-earmark-text"></i> 文件格式说明
                    </h5>
                </div>
                <div class="card-body">
                    <h6>患者名单文件 (patients.xlsx)</h6>
                    <ul class="small">
                        <li>患者姓名 - 必需</li>
                        <li>住院医生 - 必需</li>
                        <li>科室 - 可选</li>
                        <li>床号 - 可选</li>
                        <li>住院号 - 可选</li>
                    </ul>

                    <h6 class="mt-3">绩效数据文件 (.csv)</h6>
                    <ul class="small">
                        <li>xm (患者姓名) - 必需</li>
                        <li>xz (协助点数) - 必需</li>
                        <li>zx (执行点数) - 必需</li>
                        <li>医生姓名 - 可选</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<style>
.step-item {
    display: flex;
    align-items: flex-start;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 5px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    const selectedFiles = document.getElementById('selectedFiles');
    const form = document.getElementById('calculateForm');
    const submitBtn = document.getElementById('submitBtn');

    // 拖拽上传功能
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        handleFiles(files);
    });

    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });

    function handleFiles(files) {
        selectedFiles.innerHTML = '';
        const requiredFiles = ['patients.xlsx', '1.csv', '2.csv', '3.csv', 'icu.csv'];
        const uploadedFiles = Array.from(files).map(f => f.name);
        
        Array.from(files).forEach(file => {
            const isRequired = requiredFiles.includes(file.name);
            const fileItem = document.createElement('div');
            fileItem.className = `list-group-item d-flex justify-content-between align-items-center ${isRequired ? 'list-group-item-success' : 'list-group-item-warning'}`;
            
            fileItem.innerHTML = `
                <div>
                    <i class="bi bi-file-earmark${file.name.endsWith('.xlsx') ? '-excel' : '-text'}"></i>
                    <strong>${file.name}</strong>
                    <span class="text-muted">(${formatFileSize(file.size)})</span>
                    ${isRequired ? '<span class="badge bg-success ms-2">必需</span>' : '<span class="badge bg-warning ms-2">可选</span>'}
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile('${file.name}')">
                    <i class="bi bi-x"></i>
                </button>
            `;
            
            selectedFiles.appendChild(fileItem);
        });

        // 检查必需文件
        const missingFiles = requiredFiles.filter(f => !uploadedFiles.includes(f));
        if (missingFiles.length > 0) {
            const missingAlert = document.createElement('div');
            missingAlert.className = 'alert alert-warning mt-2';
            missingAlert.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i>
                <strong>缺少必需文件：</strong> ${missingFiles.join(', ')}
            `;
            selectedFiles.appendChild(missingAlert);
            submitBtn.disabled = true;
        } else {
            submitBtn.disabled = false;
        }

        fileList.style.display = files.length > 0 ? 'block' : 'none';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    window.removeFile = function(fileName) {
        // 从文件输入中移除文件（HTML5 FileList 是只读的，所以需要重新创建）
        const dt = new DataTransfer();
        const files = fileInput.files;
        
        for (let i = 0; i < files.length; i++) {
            if (files[i].name !== fileName) {
                dt.items.add(files[i]);
            }
        }
        
        fileInput.files = dt.files;
        handleFiles(fileInput.files);
    };

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        if (fileInput.files.length === 0) {
            e.preventDefault();
            alert('请先选择要上传的文件');
            return;
        }

        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>正在上传...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}