{% extends "base.html" %}

{% block title %}工作台 - 医院科室绩效计算器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>欢迎回来，{{ current_user.doctor_name or current_user.username }}</h2>
                <p class="text-muted mb-0">今天是 {{ current_time.strftime('%Y年%m月%d日') }}</p>
            </div>
            <div>
                <a href="{{ url_for('calculate') }}" class="btn btn-primary">
                    <i class="bi bi-calculator"></i> 新建计算任务
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50">总任务数</h6>
                        <h3 class="mb-0">{{ tasks|length }}</h3>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-list-task" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50">已完成</h6>
                        <h3 class="mb-0">{{ tasks|selectattr("status", "equalto", "completed")|list|length }}</h3>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50">处理中</h6>
                        <h3 class="mb-0">{{ tasks|selectattr("status", "equalto", "processing")|list|length }}</h3>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-arrow-repeat" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title text-white-50">失败</h6>
                        <h3 class="mb-0">{{ tasks|selectattr("status", "equalto", "failed")|list|length }}</h3>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-x-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> 最近的计算任务
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="statusFilter" id="all" value="all" checked>
                    <label class="btn btn-outline-secondary" for="all">全部</label>
                    
                    <input type="radio" class="btn-check" name="statusFilter" id="completed" value="completed">
                    <label class="btn btn-outline-success" for="completed">已完成</label>
                    
                    <input type="radio" class="btn-check" name="statusFilter" id="processing" value="processing">
                    <label class="btn btn-outline-warning" for="processing">处理中</label>
                    
                    <input type="radio" class="btn-check" name="statusFilter" id="failed" value="failed">
                    <label class="btn btn-outline-danger" for="failed">失败</label>
                </div>
            </div>
            <div class="card-body">
                {% if tasks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>完成时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr class="task-row" data-status="{{ task.status }}">
                                <td>
                                    <strong>{{ task.task_name }}</strong>
                                    {% if task.error_message %}
                                    <br><small class="text-danger">{{ task.error_message[:100] }}...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.status == 'completed' %}
                                        <span class="badge bg-success status-badge">
                                            <i class="bi bi-check-circle"></i> 已完成
                                        </span>
                                    {% elif task.status == 'processing' %}
                                        <span class="badge bg-warning status-badge">
                                            <i class="bi bi-arrow-repeat"></i> 处理中
                                        </span>
                                    {% elif task.status == 'failed' %}
                                        <span class="badge bg-danger status-badge">
                                            <i class="bi bi-x-circle"></i> 失败
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary status-badge">
                                            <i class="bi bi-clock"></i> 等待中
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    {% if task.completed_at %}
                                        <small>{{ task.completed_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    {% else %}
                                        <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if task.status == 'completed' and task.result_file %}
                                            <a href="{{ url_for('download_result', task_id=task.id) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-download"></i> 下载
                                            </a>
                                        {% endif %}
                                        
                                        {% if task.status == 'processing' %}
                                            <button class="btn btn-outline-info btn-sm" onclick="showTaskProgress({{ task.id }})">
                                                <i class="bi bi-eye"></i> 查看
                                            </button>
                                        {% endif %}
                                        
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteTask({{ task.id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                    <h5 class="mt-3 text-muted">暂无计算任务</h5>
                    <p class="text-muted">点击上方按钮创建您的第一个计算任务</p>
                    <a href="{{ url_for('calculate') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建任务
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 任务进度模态框 -->
<div class="modal fade" id="taskProgressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务进度</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskProgressContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在获取任务进度...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 状态筛选功能
    const statusFilters = document.querySelectorAll('input[name="statusFilter"]');
    const taskRows = document.querySelectorAll('.task-row');
    
    statusFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const selectedStatus = this.value;
            
            taskRows.forEach(row => {
                const taskStatus = row.getAttribute('data-status');
                if (selectedStatus === 'all' || taskStatus === selectedStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
});

function showTaskProgress(taskId) {
    const modal = new bootstrap.Modal(document.getElementById('taskProgressModal'));
    modal.show();
    
    // 这里可以通过AJAX获取任务进度
    fetch(`/api/task/${taskId}/progress`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('taskProgressContent').innerHTML = `
                <div class="progress mb-3">
                    <div class="progress-bar" style="width: ${data.progress}%">${data.progress}%</div>
                </div>
                <p><strong>当前状态：</strong> ${data.status}</p>
                <p><strong>处理步骤：</strong> ${data.current_step}</p>
                <p><strong>开始时间：</strong> ${data.start_time}</p>
            `;
        })
        .catch(error => {
            document.getElementById('taskProgressContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 获取任务进度失败：${error.message}
                </div>
            `;
        });
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
        fetch(`/api/task/${taskId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.error);
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}

// 自动刷新处理中的任务状态
setInterval(function() {
    const processingRows = document.querySelectorAll('.task-row[data-status="processing"]');
    if (processingRows.length > 0) {
        location.reload();
    }
}, 30000); // 每30秒刷新一次
</script>
{% endblock %}