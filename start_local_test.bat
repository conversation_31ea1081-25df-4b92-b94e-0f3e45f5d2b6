@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

echo 🏥 医院科室绩效计算器 - 本地测试环境启动
echo ============================================

REM 检查Python
echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 创建虚拟环境
if not exist "web_venv" (
    echo 🔧 创建Python虚拟环境...
    python -m venv web_venv
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call web_venv\Scripts\activate

REM 安装依赖
echo 📦 安装/更新依赖包...
python -m pip install --upgrade pip
pip install -r web_requirements.txt

REM 创建必要目录
echo 📁 创建必要目录...
if not exist "uploads" mkdir uploads
if not exist "output" mkdir output
if not exist "logs" mkdir logs
if not exist "test_data" mkdir test_data

REM 复制环境变量文件
if not exist ".env" (
    echo ⚙️ 创建环境变量文件...
    copy .env.example .env
    echo ✏️ 请编辑 .env 文件配置本地测试参数
)

REM 初始化数据库
echo 🗄️ 初始化数据库...
python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ 数据库表创建成功！')"
if errorlevel 1 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
)

REM 创建测试数据
echo 📊 创建测试数据...
(
echo import pandas as pd
echo import os
echo.
echo print^("创建测试数据文件..."^)
echo.
echo # 确保测试目录存在
echo os.makedirs^('test_data', exist_ok=True^)
echo.
echo # 创建患者测试数据
echo patients_data = {
echo     '患者姓名': ['张三', '李四', '王五', '赵六', '陈七', '刘八', '黄九', '朱十'],
echo     '住院医生': ['赖红琳', '吴西雅', '童波', '夏顺生', '邹国明', '李凡', '陈小永', '廖丽军'],
echo     '科室': ['呼吸内科'] * 8,
echo     '床号': ['101', '102', '103', '104', '105', '106', '107', '108'],
echo     '住院号': ['2025001', '2025002', '2025003', '2025004', '2025005', '2025006', '2025007', '2025008']
echo }
echo.
echo df_patients = pd.DataFrame^(patients_data^)
echo df_patients.to_excel^('test_data/patients.xlsx', index=False^)
echo print^("✅ 创建 patients.xlsx"^)
echo.
echo # 创建绩效数据
echo def create_performance_csv^(filename, patients, base_xz=10, base_zx=25^):
echo     performance_data = {
echo         'xm': patients,
echo         'xz': [base_xz + i*2 for i in range^(len^(patients^)^)],
echo         'zx': [base_zx + i*3 for i in range^(len^(patients^)^)],
echo         'name': [f'检查项目{i+1}' for i in range^(len^(patients^)^)],
echo     }
echo     df = pd.DataFrame^(performance_data^)
echo     df.to_csv^(f'test_data/{filename}', index=False, encoding='utf-8'^)
echo     print^(f"✅ 创建 {filename}"^)
echo.
echo # 创建各个病区的测试数据
echo create_performance_csv^('1.csv', ['张三', '李四'], 10, 25^)
echo create_performance_csv^('2.csv', ['王五', '赵六'], 12, 28^)
echo create_performance_csv^('3.csv', ['陈七', '刘八'], 15, 30^)
echo create_performance_csv^('icu.csv', ['黄九', '朱十'], 20, 35^)
echo.
echo print^("🎉 所有测试数据创建完成！"^)
echo print^("\\n📁 测试文件列表："^)
echo for file in sorted^(os.listdir^('test_data'^)^):
echo     print^(f"   test_data/{file}"^)
) > create_test_data.py

python create_test_data.py
del create_test_data.py

echo.
echo 🎉 本地测试环境准备完成！
echo.
echo 📋 测试清单：
echo    ✅ Python虚拟环境已创建并激活
echo    ✅ 依赖包已安装
echo    ✅ 数据库已初始化
echo    ✅ 测试数据已生成
echo    ✅ 必要目录已创建
echo.
echo 🚀 启动Web应用...
echo 📱 应用将在 http://localhost:5000 启动
echo 🔑 默认管理员账号: admin / admin123
echo.
echo 🧪 开始测试：
echo    1. 在浏览器打开 http://localhost:5000
echo    2. 注册新用户或使用管理员账号登录
echo    3. 上传 test_data\ 目录中的测试文件
echo    4. 执行绩效计算测试
echo.
echo ❌ 按 Ctrl+C 停止服务器
echo ============================================

REM 启动Flask应用
python app.py

pause