#!/bin/bash

echo "医院科室绩效计算器启动脚本"
echo "================================"

# 检查Python环境
echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3环境，请先安装Python 3.7+"
    exit 1
fi

# 检查依赖包
echo "正在检查依赖包..."
if ! python3 -c "import pandas" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 启动程序
echo "启动绩效计算器..."
python3 main.py

if [ $? -ne 0 ]; then
    echo "程序异常退出"
    read -p "按任意键继续..."
fi
