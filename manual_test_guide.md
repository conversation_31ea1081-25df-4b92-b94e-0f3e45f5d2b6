# 手动测试指南

## 🚀 启动应用

```bash
cd "/Users/<USER>/PythonProjects/科室绩效计算器"
source web_venv/bin/activate
python app.py --port 5001
```

## 🌐 访问应用

打开浏览器访问: **http://localhost:5001**

## 🔑 登录信息

- **用户名**: `admin`
- **密码**: `admin123`

## 📁 测试文件

在 `test_data/` 目录中有以下测试文件：
- `patients.xlsx` - 患者名单
- `1.csv` - 一病区数据
- `2.csv` - 二病区数据  
- `3.csv` - 三病区数据
- `icu.csv` - 呼吸ICU数据

## 🧪 测试步骤

1. **登录系统**
   - 使用管理员账号登录

2. **进入绩效计算页面**
   - 点击导航栏的"绩效计算"

3. **上传文件**
   - 点击"选择文件"按钮
   - 选择 `test_data` 目录中的所有5个文件
   - 确保所有必需文件都已上传

4. **配置任务**
   - 设置任务名称
   - 选择重复数据处理策略

5. **开始计算**
   - 点击"开始计算"按钮
   - 观察控制台输出的调试信息

## 🔍 调试信息

当你点击"开始计算"时，控制台会显示详细的调试信息，包括：
- 文件检查结果
- 数据加载过程
- 计算执行步骤
- 错误信息（如果有）

## 📊 期望结果

成功完成后应该看到：
- 医生数量: 8
- 医疗组数: 4  
- 患者数量: 8
- 记录数量: 8
- 生成Excel报告

## ❗ 可能的问题

如果看到"绩效数据加载失败"，请检查：
1. 文件是否正确上传
2. 文件格式是否正确
3. 控制台的详细错误信息

## 🆘 故障排除

如果遇到问题，请：
1. 查看控制台的详细输出
2. 检查上传的文件大小和内容
3. 确认所有5个文件都已上传
4. 尝试重新上传文件