#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含程序的各种配置参数
"""

import os
from pathlib import Path

# 项目基本信息
PROJECT_NAME = "医院科室绩效计算器"
VERSION = "1.0.0"
AUTHOR = "AI Assistant"
DESCRIPTION = "基于原VBA代码重构的现代化Python版本"

# 文件路径配置
BASE_DIR = Path(__file__).parent
LOGS_DIR = BASE_DIR / "logs"
OUTPUT_DIR = BASE_DIR / "output"
TEMPLATES_DIR = BASE_DIR / "templates"

# 确保目录存在
LOGS_DIR.mkdir(exist_ok=True)
OUTPUT_DIR.mkdir(exist_ok=True)
TEMPLATES_DIR.mkdir(exist_ok=True)

# 医生名单文件
DOCTOR_LIST_FILE = BASE_DIR / "医生名单.md"

# 数据文件配置
REQUIRED_FILES = [
    "patients.xlsx",  # 患者名单文件
    "1.csv",         # 一病区数据
    "2.csv",         # 二病区数据
    "3.csv",         # 三病区数据
    "icu.csv"        # 呼吸ICU数据
]

# 数据源描述
DATA_SOURCE_DESCRIPTIONS = {
    "1.csv": "一病区",
    "2.csv": "二病区", 
    "3.csv": "三病区",
    "icu.csv": "呼吸ICU"
}

# 患者数据必需列
PATIENT_REQUIRED_COLUMNS = ["患者姓名", "住院医生"]
PATIENT_OPTIONAL_COLUMNS = ["科室", "床号", "住院号"]

# 绩效数据必需列
PERFORMANCE_REQUIRED_COLUMNS = ["xm", "xz", "zx"]  # 姓名、协助点数、执行点数
PERFORMANCE_OPTIONAL_COLUMNS = ["name", "doctor", "ys", "医生", "住院医生"]  # 项目名称、医生信息

# 默认医生分组配置
DEFAULT_DOCTOR_GROUPS = {
    "赖红琳组": ["赖红琳", "李凡", "陈小永"],
    "吴西雅组": ["廖丽军", "吴西雅", "吴海凤"],
    "童波组": ["童波", "刘娜", "唐斌"],
    "夏顺生组": ["梁莹", "夏顺生", "陈卫群"],
    "邹国明组": ["邹国明", "周洪"],
    "其他组": ["郭玲玲", "李星", "张琦", "黄颖", "欧阳国泉", "郭猷殚"]
}

# GUI配置
GUI_CONFIG = {
    "window_title": f"{PROJECT_NAME} v{VERSION}",
    "window_size": "900x700",
    "min_window_size": (800, 600),
    "font_family": "Arial",
    "font_size": 9,
    "log_max_lines": 100,  # 日志显示最大行数
    "preview_max_rows": 1000,  # 数据预览最大行数
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# 报告配置
REPORT_CONFIG = {
    "excel_engine": "openpyxl",
    "sheet_names": {
        "individual": "医生个人绩效",
        "group": "医疗组汇总",
        "detailed": "详细绩效数据",
        "log": "数据处理日志",
        "summary": "报告汇总"
    },
    "auto_adjust_column_width": True,
    "max_column_width": 50,
    "header_style": {
        "font_bold": True,
        "font_color": "FFFFFF",
        "bg_color": "366092"
    }
}

# 数据处理配置
DATA_CONFIG = {
    "duplicate_strategy": "keep_last",  # 去重策略: keep_first, keep_last, remove_all
    "encoding_attempts": ["utf-8", "gbk", "gb2312", "utf-8-sig"],  # CSV编码尝试顺序
    "chunk_size": 10000,  # 大文件分块处理大小
    "memory_limit": 500 * 1024 * 1024,  # 内存限制 500MB
}

# 错误消息配置
ERROR_MESSAGES = {
    "folder_not_found": "选择的文件夹不存在",
    "missing_files": "文件夹中缺少必需的数据文件",
    "invalid_patient_data": "患者数据格式不正确",
    "invalid_performance_data": "绩效数据格式不正确",
    "calculation_failed": "绩效计算失败",
    "export_failed": "报告导出失败",
    "no_data": "没有可处理的数据",
}

# 成功消息配置
SUCCESS_MESSAGES = {
    "folder_validated": "文件夹验证成功",
    "patients_processed": "患者数据处理完成",
    "calculation_completed": "绩效计算完成",
    "report_exported": "报告导出成功",
}

def get_config(section: str = None):
    """
    获取配置信息
    
    Args:
        section: 配置节名称，如果为None则返回所有配置
    
    Returns:
        配置信息
    """
    all_config = {
        "project": {
            "name": PROJECT_NAME,
            "version": VERSION,
            "author": AUTHOR,
            "description": DESCRIPTION
        },
        "paths": {
            "base_dir": BASE_DIR,
            "logs_dir": LOGS_DIR,
            "output_dir": OUTPUT_DIR,
            "templates_dir": TEMPLATES_DIR,
            "doctor_list_file": DOCTOR_LIST_FILE
        },
        "data": DATA_CONFIG,
        "gui": GUI_CONFIG,
        "log": LOG_CONFIG,
        "report": REPORT_CONFIG,
        "doctor_groups": DEFAULT_DOCTOR_GROUPS,
        "required_files": REQUIRED_FILES,
        "data_sources": DATA_SOURCE_DESCRIPTIONS,
        "columns": {
            "patient_required": PATIENT_REQUIRED_COLUMNS,
            "patient_optional": PATIENT_OPTIONAL_COLUMNS,
            "performance_required": PERFORMANCE_REQUIRED_COLUMNS,
            "performance_optional": PERFORMANCE_OPTIONAL_COLUMNS
        },
        "messages": {
            "error": ERROR_MESSAGES,
            "success": SUCCESS_MESSAGES
        }
    }
    
    if section:
        return all_config.get(section, {})
    return all_config
