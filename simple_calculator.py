#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的绩效计算模块
专门为Web应用设计，避免复杂的依赖关系
"""

import pandas as pd
import os
from datetime import datetime
import json

class SimplePerformanceCalculator:
    """简化的绩效计算器"""
    
    def __init__(self):
        self.patient_data = None
        self.performance_data = {}
        self.doctor_groups = self._load_doctor_groups()
        
    def _load_doctor_groups(self):
        """加载医生分组配置"""
        # 默认分组
        default_groups = {
            "赖红琳组": ["赖红琳", "李凡", "陈小永"],
            "吴西雅组": ["廖丽军", "吴西雅", "吴海凤"],
            "童波组": ["童波", "刘娜", "唐斌"],
            "夏顺生组": ["梁莹", "夏顺生", "陈卫群"],
            "邹国明组": ["邹国明", "周洪"],
            "其他组": ["郭玲玲", "李星", "张琦", "黄颖", "欧阳国泉", "郭猷殚"]
        }
        
        # 尝试加载配置文件
        config_file = "医生分组配置.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    
                    # 检查配置文件格式
                    if 'group_doctors' in saved_config:
                        # 新格式：使用group_doctors字段
                        groups = saved_config['group_doctors']
                        print(f"✅ 从配置文件加载医生分组: {len(groups)} 个分组")
                        return groups
                    elif isinstance(saved_config, dict) and all(isinstance(v, list) for v in saved_config.values()):
                        # 旧格式：直接是分组字典
                        print(f"✅ 从配置文件加载医生分组: {len(saved_config)} 个分组")
                        return saved_config
                    else:
                        print("⚠️ 配置文件格式不正确，使用默认分组")
                        
            except Exception as e:
                print(f"⚠️ 加载医生分组配置失败: {e}，使用默认分组")
        
        print(f"✅ 使用默认医生分组: {len(default_groups)} 个分组")
        return default_groups
    
    def load_patient_data(self, file_path):
        """加载患者数据"""
        try:
            self.patient_data = pd.read_excel(file_path)
            print(f"✅ 患者数据加载成功: {len(self.patient_data)} 条记录")
            print(f"   列名: {list(self.patient_data.columns)}")
            return True
        except Exception as e:
            print(f"❌ 患者数据加载失败: {e}")
            return False
    
    def remove_duplicates(self, strategy='keep_first'):
        """去除重复数据"""
        if self.patient_data is None:
            return False
            
        try:
            original_count = len(self.patient_data)
            
            # 使用患者姓名+住院医生作为复合键
            key_columns = ['患者姓名', '住院医生']
            
            # 检查是否有重复
            duplicates = self.patient_data.duplicated(subset=key_columns, keep=False)
            duplicate_count = duplicates.sum()
            
            if duplicate_count > 0:
                print(f"⚠️ 发现 {duplicate_count} 条重复记录")
                
                # 根据策略去重
                if strategy == 'keep_first':
                    self.patient_data = self.patient_data.drop_duplicates(subset=key_columns, keep='first')
                elif strategy == 'keep_last':
                    self.patient_data = self.patient_data.drop_duplicates(subset=key_columns, keep='last')
                elif strategy == 'remove_all':
                    # 删除所有重复记录
                    self.patient_data = self.patient_data[~duplicates]
                
                removed_count = original_count - len(self.patient_data)
                print(f"✅ 去重完成，删除 {removed_count} 条记录，剩余 {len(self.patient_data)} 条")
            else:
                print("✅ 未发现重复记录")
            
            return True
        except Exception as e:
            print(f"❌ 去重处理失败: {e}")
            return False
    
    def load_performance_data(self, csv_files):
        """加载绩效数据"""
        print(f"🔄 开始加载绩效数据，共 {len(csv_files)} 个文件")
        
        self.performance_data = {}
        total_records = 0
        successful_loads = 0
        
        data_source_names = {
            '1.csv': '一病区',
            '2.csv': '二病区',
            '3.csv': '三病区',
            'icu.csv': '呼吸ICU'
        }
        
        for file_key, file_path in csv_files.items():
            print(f"📄 处理文件: {file_key} -> {file_path}")
            
            if os.path.exists(file_path):
                try:
                    # 检查文件大小
                    file_size = os.path.getsize(file_path)
                    print(f"   文件大小: {file_size} 字节")
                    
                    if file_size == 0:
                        print(f"   ⚠️ 文件为空: {file_path}")
                        continue
                    
                    # 尝试读取CSV，支持多种编码
                    df = None
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']

                    for encoding in encodings:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            print(f"   📄 成功读取文件 (编码: {encoding})")
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            print(f"   ❌ 读取失败 (编码: {encoding}): {e}")
                            continue

                    if df is None:
                        print(f"   ❌ 无法使用任何编码读取文件: {file_path}")
                        continue
                    print(f"   📊 数据行数: {len(df)}")
                    print(f"   📊 数据列数: {len(df.columns)}")
                    print(f"   📊 列名: {list(df.columns)}")
                    
                    if len(df) == 0:
                        print(f"   ⚠️ 文件无数据行: {file_path}")
                        continue
                    
                    # 检查必需列
                    required_cols = ['xm', 'xz', 'zx']
                    missing_cols = [col for col in required_cols if col not in df.columns]
                    if missing_cols:
                        print(f"   ❌ 缺少必需列: {missing_cols}")
                        continue
                    
                    self.performance_data[file_key] = df
                    total_records += len(df)
                    successful_loads += 1
                    source_name = data_source_names.get(file_key, file_key)
                    print(f"   ✅ {source_name}数据加载成功: {len(df)} 条记录")
                    
                except Exception as e:
                    print(f"   ❌ {file_key} 加载失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"   ❌ 文件不存在: {file_path}")
        
        print(f"📊 绩效数据加载汇总:")
        print(f"   成功加载: {successful_loads}/{len(csv_files)} 个文件")
        print(f"   总记录数: {total_records} 条")
        print(f"   加载的数据源: {list(self.performance_data.keys())}")
        
        if len(self.performance_data) == 0:
            print(f"❌ 没有成功加载任何绩效数据文件")
            return False
        
        print(f"✅ 绩效数据加载完成")
        return True
    
    def calculate_performance(self):
        """计算绩效"""
        if self.patient_data is None or not self.performance_data:
            return None
        
        try:
            print("🔄 开始绩效计算...")
            
            # 创建医生-分组映射
            doctor_to_group = {}
            for group_name, doctors in self.doctor_groups.items():
                for doctor in doctors:
                    doctor_to_group[doctor] = group_name
            
            # 汇总结果
            doctor_performance = {}  # 医生 -> {协助点数, 执行点数, 患者数量, 数据记录数}
            group_performance = {}   # 医疗组 -> {协助点数, 执行点数, 患者数量, 数据记录数, 医生数}
            detailed_data = []       # 详细记录
            
            # 处理每个数据源
            for file_key, perf_data in self.performance_data.items():
                source_name = {
                    '1.csv': '一病区',
                    '2.csv': '二病区', 
                    '3.csv': '三病区',
                    'icu.csv': '呼吸ICU'
                }.get(file_key, file_key)
                
                print(f"  处理 {source_name}...")
                
                # 处理每条绩效记录
                for _, perf_row in perf_data.iterrows():
                    patient_name = perf_row.get('xm', '')
                    assist_points = float(perf_row.get('xz', 0))
                    execute_points = float(perf_row.get('zx', 0))
                    
                    # 查找对应的患者和医生
                    patient_matches = self.patient_data[self.patient_data['患者姓名'] == patient_name]
                    
                    if len(patient_matches) > 0:
                        # 使用第一个匹配的患者记录
                        patient_row = patient_matches.iloc[0]
                        doctor_name = patient_row['住院医生']
                        
                        # 获取医生所属分组
                        group_name = doctor_to_group.get(doctor_name, '其他组')
                        
                        # 累计医生绩效
                        if doctor_name not in doctor_performance:
                            doctor_performance[doctor_name] = {
                                'group': group_name,
                                'assist_points': 0,
                                'execute_points': 0,
                                'patient_count': set(),
                                'record_count': 0
                            }
                        
                        doctor_performance[doctor_name]['assist_points'] += assist_points
                        doctor_performance[doctor_name]['execute_points'] += execute_points
                        doctor_performance[doctor_name]['patient_count'].add(patient_name)
                        doctor_performance[doctor_name]['record_count'] += 1
                        
                        # 累计分组绩效
                        if group_name not in group_performance:
                            group_performance[group_name] = {
                                'assist_points': 0,
                                'execute_points': 0,
                                'patient_count': set(),
                                'record_count': 0,
                                'doctors': set()
                            }
                        
                        group_performance[group_name]['assist_points'] += assist_points
                        group_performance[group_name]['execute_points'] += execute_points
                        group_performance[group_name]['patient_count'].add(patient_name)
                        group_performance[group_name]['record_count'] += 1
                        group_performance[group_name]['doctors'].add(doctor_name)
                        
                        # 记录详细数据
                        detailed_data.append({
                            '患者姓名': patient_name,
                            '住院医生': doctor_name,
                            '医疗组': group_name,
                            '协助点数': assist_points,
                            '执行点数': execute_points,
                            '综合点数': assist_points + execute_points,
                            '数据源': source_name
                        })
            
            # 整理结果
            results = {
                'doctor_performance': [],
                'group_performance': [],
                'detailed_data': detailed_data,
                'summary': {
                    'total_doctors': len(doctor_performance),
                    'total_groups': len(group_performance),
                    'total_patients': len(set(record['患者姓名'] for record in detailed_data)),
                    'total_records': len(detailed_data)
                }
            }
            
            # 医生个人绩效
            for doctor, data in doctor_performance.items():
                results['doctor_performance'].append({
                    '医生姓名': doctor,
                    '医疗组': data['group'],
                    '患者数量': len(data['patient_count']),
                    '数据记录数': data['record_count'],
                    '协助点数': data['assist_points'],
                    '执行点数': data['execute_points'],
                    '综合点数': data['assist_points'] + data['execute_points']
                })
            
            # 医疗组汇总
            for group, data in group_performance.items():
                results['group_performance'].append({
                    '医疗组': group,
                    '医生数量': len(data['doctors']),
                    '患者数量': len(data['patient_count']),
                    '数据记录数': data['record_count'],
                    '协助点数': data['assist_points'],
                    '执行点数': data['execute_points'],
                    '综合点数': data['assist_points'] + data['execute_points']
                })
            
            print(f"✅ 绩效计算完成!")
            print(f"   医生数量: {results['summary']['total_doctors']}")
            print(f"   医疗组数: {results['summary']['total_groups']}")
            print(f"   患者数量: {results['summary']['total_patients']}")
            print(f"   记录数量: {results['summary']['total_records']}")
            
            return results
            
        except Exception as e:
            print(f"❌ 绩效计算失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_excel_report(self, results, output_path):
        """生成Excel报告"""
        try:
            print(f"📊 生成Excel报告: {output_path}")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 医生个人绩效
                df_doctors = pd.DataFrame(results['doctor_performance'])
                df_doctors.to_excel(writer, sheet_name='医生个人绩效', index=False)
                
                # 医疗组汇总
                df_groups = pd.DataFrame(results['group_performance'])
                df_groups.to_excel(writer, sheet_name='医疗组汇总', index=False)
                
                # 详细绩效数据
                df_detailed = pd.DataFrame(results['detailed_data'])
                df_detailed.to_excel(writer, sheet_name='详细绩效数据', index=False)
                
                # 医生分组明细
                group_details = []
                for group_name, doctors in self.doctor_groups.items():
                    for doctor in doctors:
                        group_details.append({
                            '医生姓名': doctor,
                            '所属医疗组': group_name
                        })
                df_group_details = pd.DataFrame(group_details)
                df_group_details.to_excel(writer, sheet_name='医生分组明细', index=False)
                
                # 医疗组结构
                group_structure = []
                for group_name, doctors in self.doctor_groups.items():
                    group_structure.append({
                        '医疗组': group_name,
                        '医生数量': len(doctors),
                        '医生名单': ', '.join(doctors)
                    })
                df_structure = pd.DataFrame(group_structure)
                df_structure.to_excel(writer, sheet_name='医疗组结构', index=False)
                
                # 汇总信息
                summary_data = [
                    ['统计项目', '数值'],
                    ['总医生数', results['summary']['total_doctors']],
                    ['总医疗组数', results['summary']['total_groups']],
                    ['总患者数', results['summary']['total_patients']],
                    ['总记录数', results['summary']['total_records']],
                    ['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
                ]
                df_summary = pd.DataFrame(summary_data[1:], columns=summary_data[0])
                df_summary.to_excel(writer, sheet_name='报告汇总', index=False)
            
            print(f"✅ Excel报告生成成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Excel报告生成失败: {e}")
            return False