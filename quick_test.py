#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web应用快速测试脚本
"""

import subprocess
import time
import requests
import threading
import signal
import sys
import os

class WebAppTester:
    def __init__(self):
        self.process = None
        self.base_url = "http://127.0.0.1:5000"
        
    def start_server(self):
        """启动服务器"""
        print("🚀 启动Web服务器...")
        
        # 激活虚拟环境并启动应用
        cmd = "source web_venv/bin/activate && python app.py --host 127.0.0.1 --port 5000"
        
        self.process = subprocess.Popen(
            cmd, 
            shell=True, 
            executable='/bin/bash',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(15):  # 等待15秒
            try:
                response = requests.get(self.base_url, timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功！")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            print(".", end="", flush=True)
        
        print("\n❌ 服务器启动超时")
        return False
    
    def test_endpoints(self):
        """测试各个端点"""
        print("\n🧪 测试Web端点...")
        
        endpoints = [
            ('GET', '/', '主页'),
            ('GET', '/login', '登录页面'),
            ('GET', '/register', '注册页面'),
        ]
        
        results = []
        for method, path, name in endpoints:
            try:
                url = f"{self.base_url}{path}"
                response = requests.get(url, timeout=5)
                
                if response.status_code == 200:
                    print(f"✅ {name}: 状态码 {response.status_code}")
                    results.append(True)
                else:
                    print(f"❌ {name}: 状态码 {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {name}: 请求失败 - {e}")
                results.append(False)
        
        return all(results)
    
    def test_user_registration(self):
        """测试用户注册功能"""
        print("\n👤 测试用户注册...")
        
        try:
            # 获取注册页面
            response = requests.get(f"{self.base_url}/register")
            if response.status_code != 200:
                print("❌ 无法访问注册页面")
                return False
            
            # 测试注册请求
            register_data = {
                'username': 'test_web_user',
                'email': '<EMAIL>',
                'password': 'test123',
                'doctor_name': '测试医生'
            }
            
            response = requests.post(
                f"{self.base_url}/register",
                data=register_data,
                allow_redirects=False
            )
            
            if response.status_code in [200, 302]:  # 成功或重定向
                print("✅ 用户注册功能正常")
                return True
            else:
                print(f"❌ 注册失败: 状态码 {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 注册测试失败: {e}")
            return False
    
    def test_admin_login(self):
        """测试管理员登录"""
        print("\n🔐 测试管理员登录...")
        
        try:
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            # 创建会话
            session = requests.Session()
            
            # 获取登录页面
            response = session.get(f"{self.base_url}/login")
            if response.status_code != 200:
                print("❌ 无法访问登录页面")
                return False
            
            # 尝试登录
            response = session.post(
                f"{self.base_url}/login",
                data=login_data,
                allow_redirects=False
            )
            
            if response.status_code in [200, 302]:
                print("✅ 管理员登录功能正常")
                
                # 测试访问工作台
                response = session.get(f"{self.base_url}/dashboard")
                if response.status_code == 200:
                    print("✅ 工作台访问正常")
                    return True
                else:
                    print(f"❌ 工作台访问失败: {response.status_code}")
                    return False
            else:
                print(f"❌ 登录失败: 状态码 {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录测试失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.process:
            print("\n🛑 停止服务器...")
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            print("✅ 服务器已停止")
    
    def run_tests(self):
        """运行所有测试"""
        print("🏥 医院科室绩效计算器 - Web应用测试")
        print("=" * 50)
        
        try:
            # 启动服务器
            if not self.start_server():
                return False
            
            # 运行测试
            tests = [
                ("端点访问测试", self.test_endpoints),
                ("用户注册测试", self.test_user_registration),
                ("管理员登录测试", self.test_admin_login),
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} 异常: {e}")
                    results.append((test_name, False))
            
            # 显示结果
            print("\n" + "=" * 50)
            print("🎯 测试结果汇总:")
            all_passed = True
            for test_name, result in results:
                status = "✅ 通过" if result else "❌ 失败"
                print(f"   {test_name}: {status}")
                if not result:
                    all_passed = False
            
            print()
            if all_passed:
                print("🎉 所有测试通过！Web应用运行正常！")
                print("🌐 您可以在浏览器中访问:")
                print(f"   {self.base_url}")
                print("🔑 使用以下账号登录:")
                print("   管理员: admin / admin123")
                print("📁 测试数据位于: test_data/ 目录")
                print("\n💡 要启动应用供手动测试，请运行:")
                print("   python app.py")
            else:
                print("⚠️ 部分测试失败，请检查错误信息")
            
            return all_passed
            
        finally:
            self.stop_server()

def main():
    # 切换到脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 运行测试
    tester = WebAppTester()
    
    def signal_handler(sig, frame):
        print("\n收到中断信号...")
        tester.stop_server()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = tester.run_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())